#!/usr/bin/env python3
"""
Test de la nouvelle logique de chargement du fichier update_Base_manquant_REF_MUT.xlsx
"""

from pathlib import Path
import logging
import pandas as pd

# Configuration du logging pour le test
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_ref_mut_loading_logic():
    """
    Test de la logique de chargement du fichier REF_MUT
    Simule les différents scénarios possibles
    """
    
    print("=== TEST DE LA LOGIQUE DE CHARGEMENT REF_MUT ===\n")
    
    # Simulation des chemins
    update_ref_mut_path = None  # Pas de chemin fourni explicitement
    df_update_ref_mut = None
    ref_mut_dict = None
    pm_dict = None
    
    # Logique copiée du code principal
    print("🔍 Test de la logique de recherche de fichier...")
    
    # Essayer d'abord le chemin fourni, puis le chemin réseau par défaut, puis le chemin local de test
    actual_update_path = None
    if update_ref_mut_path and update_ref_mut_path.exists():
        actual_update_path = update_ref_mut_path
        logging.info(f"Fichier update_Base_manquant_REF_MUT.xlsx trouvé à: {actual_update_path}")
    else:
        # Essayer le chemin réseau par défaut
        network_path = Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\update_Base_manquant_REF_MUT.xlsx")
        local_test_path = Path(r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\update_Base_manquant_REF_MUT.xlsx")
        
        # Essayer le chemin réseau avec gestion d'erreur
        try:
            if network_path.exists():
                actual_update_path = network_path
                logging.info(f"✅ Fichier update_Base_manquant_REF_MUT.xlsx trouvé sur le réseau: {network_path}")
            else:
                logging.info("Fichier update_Base_manquant_REF_MUT.xlsx non trouvé sur le réseau")
        except (OSError, PermissionError, Exception) as e:
            logging.warning(f"Erreur accès réseau pour update_Base_manquant_REF_MUT.xlsx: {e}")
        
        # Si pas trouvé sur réseau, essayer le chemin local de test
        if actual_update_path is None:
            try:
                if local_test_path.exists():
                    actual_update_path = local_test_path  
                    logging.info(f"✅ Fichier update_Base_manquant_REF_MUT.xlsx trouvé en local: {local_test_path}")
                else:
                    logging.info("Fichier update_Base_manquant_REF_MUT.xlsx non trouvé en local non plus")
            except (OSError, Exception) as e:
                logging.warning(f"Erreur accès local pour update_Base_manquant_REF_MUT.xlsx: {e}")
        
        # Message final si aucun fichier trouvé
        if actual_update_path is None:
            logging.info("ℹ️ Fichier update_Base_manquant_REF_MUT.xlsx non trouvé - continuons sans complétion REF_MUT")

    # Tentative de chargement si fichier trouvé
    if actual_update_path:
        logging.info(f"📂 Chargement du fichier update_Base_manquant_REF_MUT.xlsx depuis {actual_update_path}")
        print("⏳ Chargement update_Base_manquant_REF_MUT.xlsx...")
        
        try:
            df_update_ref_mut = pd.read_excel(
                actual_update_path, 
                engine='openpyxl',
                dtype=str,  # Tout en string pour éviter les conversions automatiques
                na_filter=False  # Éviter la conversion automatique en NaN
            )
        except (FileNotFoundError, PermissionError, Exception) as e:
            logging.error(f"❌ Erreur lors du chargement de update_Base_manquant_REF_MUT.xlsx: {e}")
            logging.info("Continuons sans le fichier update_Base_manquant_REF_MUT.xlsx")
            df_update_ref_mut = None
        
        # Vérifier que le chargement a réussi avant de l'utiliser
        if df_update_ref_mut is not None:
            logging.info(f"✅ Fichier update_Base_manquant_REF_MUT chargé: {len(df_update_ref_mut):,} lignes")
            print(f"✅ update_Base_manquant_REF_MUT chargé: {len(df_update_ref_mut):,} lignes")
        
            # Créer les dictionnaires immédiatement après le chargement réussi
            ref_mut_dict = {}
            pm_dict = {}
            
            if 'Référence contrat' in df_update_ref_mut.columns:
                for idx, row in df_update_ref_mut.iterrows():
                    ref_contrat = row.get('Référence contrat')
                    if ref_contrat and ref_contrat != '':
                        ref_mut_dict[ref_contrat] = row.to_dict()
                logging.info(f"📊 Dictionnaire REF_MUT créé avec {len(ref_mut_dict)} entrées")
            else:
                logging.warning("Colonne 'Référence contrat' non trouvée dans update_Base_manquant_REF_MUT.xlsx")
            
            if 'Sro' in df_update_ref_mut.columns:
                for idx, row in df_update_ref_mut.iterrows():
                    sro = row.get('Sro')
                    if sro and sro != '':
                        pm_dict[sro] = row.to_dict()
                logging.info(f"📊 Dictionnaire PM créé avec {len(pm_dict)} entrées")
            else:
                logging.warning("Colonne 'Sro' non trouvée dans update_Base_manquant_REF_MUT.xlsx")
        else:
            logging.info("❌ Échec du chargement de update_Base_manquant_REF_MUT.xlsx")
    else:
        logging.info("ℹ️ Fichier update_Base_manquant_REF_MUT.xlsx non trouvé - continuons sans complétion REF_MUT")

    # Résumé du test
    print(f"\n=== RÉSUMÉ DU TEST ===")
    print(f"Fichier trouvé: {'✅ OUI' if actual_update_path else '❌ NON'}")
    if actual_update_path:
        print(f"Chemin: {actual_update_path}")
    print(f"DataFrame chargé: {'✅ OUI' if df_update_ref_mut is not None else '❌ NON'}")
    if df_update_ref_mut is not None:
        print(f"Lignes: {len(df_update_ref_mut)}")
        print(f"Colonnes: {list(df_update_ref_mut.columns)}")
    print(f"Dictionnaire REF_MUT: {'✅ OUI' if ref_mut_dict else '❌ NON'}")
    if ref_mut_dict:
        print(f"Entrées REF_MUT: {len(ref_mut_dict)}")
    print(f"Dictionnaire PM: {'✅ OUI' if pm_dict else '❌ NON'}")
    if pm_dict:
        print(f"Entrées PM: {len(pm_dict)}")
        
    return {
        'file_found': actual_update_path is not None,
        'file_path': str(actual_update_path) if actual_update_path else None,
        'df_loaded': df_update_ref_mut is not None,
        'ref_mut_dict_size': len(ref_mut_dict) if ref_mut_dict else 0,
        'pm_dict_size': len(pm_dict) if pm_dict else 0
    }

if __name__ == "__main__":
    result = test_ref_mut_loading_logic()
    
    print(f"\n🎯 RÉSULTAT FINAL:")
    if result['file_found'] and result['df_loaded']:
        print("✅ TEST RÉUSSI - Fichier trouvé et chargé avec succès")
    elif not result['file_found']:
        print("ℹ️ TEST OK - Fichier non trouvé mais gestion d'erreur correcte")  
    else:
        print("❌ TEST PARTIELLEMENT ÉCHOUÉ - Fichier trouvé mais non chargé")