import re
import traceback
import logging
import pandas as pd
from pathlib import Path
from copy import copy
from openpyxl.utils.dataframe import dataframe_to_rows
import datetime
import shutil
import os
import sys
import warnings
import multiprocessing as mp
import numpy as np
import openpyxl
from openpyxl.styles import Font, Alignment
import argparse # for command line arguments
from openpyxl.utils import get_column_letter

warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')

def sanitize_filename(name):
    """Nettoie les noms de fichiers des caractères spéciaux"""
    return re.sub(r'[\\/*?:"<>|]', '_', str(name))

def setup_logging(log_file):
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

def ensure_directory_exists(directory):
    Path(directory).mkdir(parents=True, exist_ok=True)

def is_file_today(file_path):
    """Vérifie si le fichier existe et a été modifié aujourd'hui."""
    return os.path.exists(file_path) and datetime.date.fromtimestamp(os.path.getmtime(file_path)).date() == datetime.date.today()

def copy_file(source, destination):
    try:
        # Vérifier si le fichier source et le fichier destination sont identiques
        if os.path.exists(destination) and abs(os.path.getmtime(source) - os.path.getmtime(destination)) < 1:  # Ajout comparaison date de modif
             logging.info(f"Fichier déjà à jour : {os.path.basename(source)}")  # Si les fichiers sont à la même date, on ne recopie pas
             return True
        shutil.copy2(source, destination)
        logging.info(f"Fichier copié : {os.path.basename(source)}")
        return True
    except Exception as e:
        logging.error(f"Erreur copie {source} : {str(e)}")
        return False

def sanitize_excel_output(df):
    # Formater les colonnes de date au format JJ/MM/AAAA HH:MM:SS
    date_columns = ['Date ouverture du cas', 'Date résolution']
    for col in date_columns:
        if col in df.columns:
            try:
                # Vérifier si la colonne est de type datetime
                if pd.api.types.is_datetime64_any_dtype(df[col]):
                    # Compter les valeurs non-NaT
                    non_nat_count = (~df[col].isna()).sum()
                    logging.info(f"Colonne {col}: {non_nat_count} valeurs non-NaT sur {len(df)} lignes")

                    # Convertir les dates au format français JJ/MM/AAAA HH:MM:SS
                    df[col] = df[col].dt.strftime('%d/%m/%Y %H:%M:%S')
                    logging.info(f"Colonne {col} formatée au format JJ/MM/AAAA HH:MM:SS")
                else:
                    # Si la colonne n'est pas de type datetime, essayer de la convertir
                    logging.warning(f"Colonne {col} n'est pas de type datetime, tentative de conversion")
                    try:
                        # Sauvegarder la colonne originale
                        original_col = df[col].copy()

                        # Essayer de convertir en datetime
                        df[col] = pd.to_datetime(df[col], errors='coerce')

                        # Vérifier si la conversion a réussi
                        if df[col].isna().sum() > len(df) * 0.5:  # Si plus de 50% sont NaT
                            # Restaurer la colonne originale
                            df[col] = original_col
                            logging.warning(f"Conversion de {col} en datetime a échoué, trop de valeurs NaT")
                        else:
                            # Formater les dates
                            df[col] = df[col].dt.strftime('%d/%m/%Y %H:%M:%S')
                            logging.info(f"Colonne {col} convertie et formatée au format JJ/MM/AAAA HH:MM:SS")
                    except Exception as e:
                        # Restaurer la colonne originale en cas d'erreur
                        df[col] = original_col
                        logging.warning(f"Erreur lors de la conversion de {col}: {str(e)}")
            except Exception as e:
                logging.warning(f"Impossible de formater la colonne '{col}': {str(e)}")

    # Traitement spécial pour DEPT - s'assurer qu'il est en format texte sans décimales
    if 'DEPT' in df.columns:
        try:
            # Convertir en string et supprimer les décimales (.0)
            df['DEPT'] = df['DEPT'].astype(str).replace(r'\.0$', '', regex=True)
            logging.info("Colonne DEPT formatée en texte sans décimales")
        except Exception as e:
            logging.warning(f"Erreur lors du formatage de la colonne DEPT: {str(e)}")

    # Traitement spécial pour Délai_résolution - remplacer les points par des virgules
    if 'Délai_résolution' in df.columns:
        try:
            # Convertir en string et remplacer les points par des virgules
            df['Délai_résolution'] = df['Délai_résolution'].astype(str).replace('.', ',')
            logging.info("Colonne Délai_résolution formatée avec virgules au lieu de points")
        except Exception as e:
            logging.warning(f"Erreur lors du formatage de la colonne Délai_résolution: {str(e)}")

    # Nettoyer les autres colonnes
    for col in df.columns:
        try:
            if col not in date_columns or not pd.api.types.is_datetime64_any_dtype(df[col]):
                df[col] = df[col].astype(str).str.replace("=", "'=", regex=False)
                df[col] = df[col].str.strip()
        except (AttributeError, TypeError):
            logging.warning(f"Impossible de nettoyer la colonne '{col}'. Le type de données n'est pas compatible.")
            continue
    return df

def merge_datasets(extract_path, mut_path, sadira_path, oi_path):
    try:
        logging.info("Chargement des données...")
        df_extract = pd.read_excel(extract_path, engine='openpyxl')
        df_mut = pd.read_excel(mut_path, engine='openpyxl')
        df_sadira = pd.read_excel(sadira_path, engine='openpyxl')
        df_oi = pd.read_excel(oi_path, engine='openpyxl')

        for df in [df_extract, df_mut, df_sadira, df_oi]:
            df.columns = df.columns.astype(str).str.strip()

        # Vérifier la présence et le contenu des colonnes de date dans df_mut
        date_columns = ['Date ouverture du cas', 'Date résolution']
        for col in date_columns:
            if col in df_mut.columns:
                # Afficher des statistiques sur les valeurs de date
                total_rows = len(df_mut)
                non_null_count = df_mut[col].notna().sum()
                null_count = df_mut[col].isna().sum()
                logging.info(f"Colonne {col} dans df_mut: {non_null_count} valeurs non nulles, {null_count} valeurs nulles sur {total_rows} lignes")

                # Afficher quelques exemples de valeurs
                sample_values = df_mut[col].head(5).tolist()
                logging.info(f"Exemples de valeurs dans {col}: {sample_values}")
            else:
                logging.warning(f"Colonne {col} non trouvée dans df_mut")

        # Vérifier la présence des colonnes de date dans le fichier source
        date_columns = ['Date ouverture du cas', 'Date résolution']
        for col in date_columns:
            if col in df_mut.columns:
                logging.info(f"Colonne {col} trouvée dans le fichier source")
                # Afficher quelques exemples de valeurs
                sample_values = df_mut[col].head(5).tolist()
                logging.info(f"Exemples de valeurs dans {col}: {sample_values}")
                # Vérifier le type de données
                logging.info(f"Type de données de {col}: {df_mut[col].dtype}")
                # Compter les valeurs non nulles
                non_null_count = df_mut[col].notna().sum()
                logging.info(f"Nombre de valeurs non nulles dans {col}: {non_null_count} sur {len(df_mut)}")
            else:
                logging.warning(f"Colonne {col} non trouvée dans le fichier source!")

        if 'A pour cas parent' not in df_mut.columns or 'TT STIT' not in df_extract.columns:
            raise ValueError("Colonnes manquantes !")

        # Vérifier et corriger les dates manquantes dans df_mut avant la jointure
        date_columns = ['Date ouverture du cas', 'Date résolution']
        for col in date_columns:
            if col in df_mut.columns:
                missing_dates = df_mut[col].isna().sum()
                total_rows = len(df_mut)
                if missing_dates > 0:
                    logging.warning(f"Attention: {missing_dates} valeurs manquantes sur {total_rows} lignes dans la colonne {col} de df_mut avant jointure")
                    # Afficher quelques exemples de lignes avec dates manquantes
                    missing_examples = df_mut[df_mut[col].isna()].head(5)
                    logging.warning(f"Exemples de lignes avec {col} manquant:\n{missing_examples.to_string()}")

                    # Si c'est la date d'ouverture qui est manquante, essayer de la compléter
                    if col == 'Date ouverture du cas' and 'Date résolution' in df_mut.columns:
                        # Essayer de compléter les dates d'ouverture manquantes avec les dates de résolution
                        # (en soustrayant un délai moyen ou une valeur par défaut)
                        mask_missing_open = df_mut[col].isna() & df_mut['Date résolution'].notna()
                        if mask_missing_open.sum() > 0:
                            # Calculer un délai moyen pour les lignes où les deux dates sont présentes
                            valid_dates = df_mut[df_mut['Date ouverture du cas'].notna() & df_mut['Date résolution'].notna()]
                            if len(valid_dates) > 0:
                                # Convertir les dates en datetime
                                valid_dates['Date ouverture du cas'] = pd.to_datetime(valid_dates['Date ouverture du cas'], errors='coerce')
                                valid_dates['Date résolution'] = pd.to_datetime(valid_dates['Date résolution'], errors='coerce')

                                # Calculer le délai moyen en jours
                                mean_delay = (valid_dates['Date résolution'] - valid_dates['Date ouverture du cas']).mean()
                                if pd.notna(mean_delay):
                                    # Utiliser ce délai moyen pour estimer les dates d'ouverture manquantes
                                    df_mut.loc[mask_missing_open, 'Date ouverture du cas'] = pd.to_datetime(df_mut.loc[mask_missing_open, 'Date résolution']) - mean_delay
                                    logging.info(f"Complété {mask_missing_open.sum()} dates d'ouverture manquantes en utilisant un délai moyen de {mean_delay}")
                                else:
                                    # Si le délai moyen ne peut pas être calculé, utiliser une valeur par défaut (7 jours)
                                    df_mut.loc[mask_missing_open, 'Date ouverture du cas'] = pd.to_datetime(df_mut.loc[mask_missing_open, 'Date résolution']) - pd.Timedelta(days=7)
                                    logging.info(f"Complété {mask_missing_open.sum()} dates d'ouverture manquantes en utilisant un délai par défaut de 7 jours")

        merged_df = pd.merge(
            df_mut,
            df_extract[['TT STIT', 'Elément impacté', 'Sous-traitant intervention', 'Region']],
            left_on='A pour cas parent',
            right_on='TT STIT',
            how='left'
        )

        merged_df.rename(columns={
            'Elément impacté': 'PM',
            'Sous-traitant intervention': 'STIT'
        }, inplace=True)

        merged_df.insert(19, 'Région', merged_df.pop('Region'))

        df_sadira.rename(columns={
            'Référence - LT.DET': 'PM',
            'Code postal - LT.ADR': 'DEPT',
            'Propriétaire - LT.DET': 'Propriétaire',
            'Type de zone (ZTD/ZMD) – LT.DET': 'Zone'
        }, inplace=True)

        # Vérifier et nettoyer les valeurs NaN dans la colonne DEPT
        nan_count_before = df_sadira['DEPT'].isna().sum()
        if nan_count_before > 0:
            logging.warning(f"Trouvé {nan_count_before} valeurs NaN dans la colonne DEPT du fichier FTTH_Liste_PM_source_sadira.xlsx")

        # Remplacer les NaN par une chaîne vide avant de convertir en string
        df_sadira['DEPT'] = df_sadira['DEPT'].fillna('')

        # Extraire les 2 premiers chiffres du code postal uniquement pour les valeurs non vides
        df_sadira.loc[df_sadira['DEPT'] != '', 'DEPT'] = df_sadira.loc[df_sadira['DEPT'] != '', 'DEPT'].astype(str).str[:2]

        # S'assurer que DEPT est toujours en format texte
        df_sadira['DEPT'] = df_sadira['DEPT'].astype(str)

        # Remplacer 'nan' par une chaîne vide
        df_sadira['DEPT'] = df_sadira['DEPT'].replace('nan', '')

        # Afficher quelques exemples pour débogage
        logging.info(f"Exemples de valeurs DEPT après traitement: {df_sadira['DEPT'].head(10).tolist()}")

        # Vérifier les PM dans merged_df avant la jointure
        pm_count = merged_df['PM'].count()
        pm_unique_count = merged_df['PM'].nunique()
        pm_null_count = merged_df['PM'].isna().sum()
        logging.info(f"Avant jointure avec df_sadira: {pm_count} PM au total, {pm_unique_count} PM uniques, {pm_null_count} PM nuls")

        # Vérifier les PM dans df_sadira avant la jointure
        sadira_pm_count = df_sadira['PM'].count()
        sadira_pm_unique_count = df_sadira['PM'].nunique()
        logging.info(f"Dans df_sadira: {sadira_pm_count} PM au total, {sadira_pm_unique_count} PM uniques")

        # Effectuer la jointure
        merged_df = pd.merge(merged_df, df_sadira[['PM', 'DEPT', 'Propriétaire', 'Zone']], on='PM', how='left')

        # Vérifier combien de lignes ont un DEPT vide après la jointure
        dept_null_count = merged_df['DEPT'].isna().sum()
        dept_empty_count = (merged_df['DEPT'] == '').sum()
        logging.info(f"Après jointure: {dept_null_count} DEPT nuls, {dept_empty_count} DEPT vides")

        # Traitement des valeurs NaN et formatage de DEPT après la fusion
        if 'DEPT' in merged_df.columns:
            # Vérifier les valeurs NaN après la fusion
            nan_count_after = merged_df['DEPT'].isna().sum()
            if nan_count_after > 0:
                logging.warning(f"Trouvé {nan_count_after} valeurs NaN dans la colonne DEPT après la fusion")

            # Remplacer les NaN par une chaîne vide
            merged_df['DEPT'] = merged_df['DEPT'].fillna('')

            # S'assurer que DEPT est en format texte
            merged_df['DEPT'] = merged_df['DEPT'].astype(str)

            # Remplacer 'nan' par une chaîne vide
            merged_df['DEPT'] = merged_df['DEPT'].replace('nan', '')

            # Supprimer les décimales (.0) si présentes
            merged_df['DEPT'] = merged_df['DEPT'].replace(r'\.0$', '', regex=True)

            logging.info(f"Exemples de valeurs DEPT après fusion et nettoyage: {merged_df['DEPT'].head(10).tolist()}")

        df_oi.rename(columns={
            'Propriétaire - LTDET': 'Propriétaire',
            'OI': 'OI'
        }, inplace=True)
        merged_df = pd.merge(merged_df, df_oi[['Propriétaire', 'Code OI InterOP', 'OI']], on='Propriétaire', how='left')

        merged_df.drop(columns=['A pour cas parent'], inplace=True)

        if 'OC' not in merged_df.columns:
            logging.warning("La colonne 'OC' n'a pas été trouvée dans le reporting ; création de 'OC' à partir de 'Code OI InterOP'.")
            merged_df['OC'] = merged_df['Code OI InterOP']

        merged_df.dropna(how='all', inplace=True)
        merged_df.fillna('', inplace=True)

        # Suppression des doublons basés sur la colonne "Identifiant cas"
        if 'Identifiant cas' in merged_df.columns:
            # Compter les doublons avant suppression
            total_rows = len(merged_df)
            # Supprimer les doublons en gardant la première occurrence
            merged_df.drop_duplicates(subset=['Identifiant cas'], keep='first', inplace=True)
            # Compter combien de lignes ont été supprimées
            removed_rows = total_rows - len(merged_df)
            logging.info(f"Suppression de {removed_rows} doublons basés sur 'Identifiant cas'")
        else:
            logging.warning("Colonne 'Identifiant cas' non trouvée, impossible de supprimer les doublons")

        # Supprimer les lignes où "Code OI InterOP" contient "SRRA"
        if 'Code OI InterOP' in merged_df.columns:
            # Identifier les lignes où "Code OI InterOP" contient "SRRA"
            srra_mask = merged_df['Code OI InterOP'].astype(str).str.contains('SRRA', na=False)
            srra_count = srra_mask.sum()

            # Supprimer ces lignes
            if srra_count > 0:
                merged_df = merged_df[~srra_mask]
                logging.info(f"Suppression de {srra_count} lignes où 'Code OI InterOP' contient 'SRRA'")
            else:
                logging.info("Aucune ligne avec 'SRRA' dans 'Code OI InterOP' n'a été trouvée")

        logging.info(f"Colonnes finales : {merged_df.columns.tolist()}")
        return sanitize_excel_output(merged_df)

    except Exception as e:
        logging.error(f"ERREUR fusion : {str(e)}")
        logging.debug(traceback.format_exc())
        raise

def determine_facturation_eligibility(merged_df, selected_month=None):
    """Détermine l'éligibilité à la facturation selon la typologie des cas.
       Un filtre sur le mois est appliqué avant le reste du traitement.
       Seuls les tickets avec le statut "Clos" sont conservés."""
    logging.info("Détermination de l'éligibilité à la facturation...")

    # Filtrer par mois AVANT le reste du traitement
    if selected_month:
        try:
            merged_df['Mois_Résolution'] = merged_df['Mois_Résolution'].astype(str)
            merged_df = merged_df[merged_df['Mois_Résolution'] == str(selected_month)].copy()  # <--- Utilisation de .copy()
            logging.info(f"Filtrage sur le mois : {selected_month}")
        except (KeyError, TypeError):
            logging.warning(f"Colonne 'Mois_Résolution' non trouvée ou type incompatible.  Pas de filtre appliqué.")
            pass  # Ne pas filtrer si la colonne n'existe pas ou si le type est mauvais
        except Exception as e:
            logging.warning(f"Erreur lors du filtrage par mois : {e}")
            pass  # Ne pas filtrer si une autre erreur se produit

    # Filtrer pour ne garder que les tickets avec le statut "Clos"
    if 'Statut' in merged_df.columns:
        # Afficher les valeurs uniques de la colonne Statut pour débogage
        unique_statuses = merged_df['Statut'].unique()
        logging.info(f"Valeurs uniques dans la colonne Statut: {unique_statuses}")

        # Nettoyer la colonne Statut (supprimer les espaces, normaliser la casse)
        merged_df['Statut'] = merged_df['Statut'].astype(str).str.strip().str.title()

        # Afficher les valeurs uniques après nettoyage
        unique_statuses_after = merged_df['Statut'].unique()
        logging.info(f"Valeurs uniques dans la colonne Statut après nettoyage: {unique_statuses_after}")

        # Filtrer pour ne garder que les tickets avec le statut "Clos"
        total_rows = len(merged_df)
        merged_df = merged_df[merged_df['Statut'].str.contains('Clos', case=False, na=False)].copy()
        filtered_rows = total_rows - len(merged_df)
        logging.info(f"Filtrage sur le statut 'Clos' : {filtered_rows} lignes supprimées, {len(merged_df)} lignes restantes")

        # Vérifier qu'il ne reste plus de tickets avec d'autres statuts
        remaining_statuses = merged_df['Statut'].unique()
        logging.info(f"Statuts restants après filtrage: {remaining_statuses}")
    else:
        logging.warning("Colonne 'Statut' non trouvée, impossible de filtrer sur le statut 'Clos'")

    # Convertir les colonnes de date à DateTime
    for col in ['Date ouverture du cas', 'Date résolution']:
        try:
            # Vérifier si la colonne existe
            if col in merged_df.columns:
                # Sauvegarder la colonne originale avant conversion
                original_col = merged_df[col].copy()

                # Afficher quelques valeurs pour débogage
                logging.info(f"Exemples de valeurs dans la colonne {col} avant conversion: {merged_df[col].head(5).tolist()}")

                # Compter les valeurs non nulles avant conversion
                non_null_before = merged_df[col].notna().sum()
                logging.info(f"Avant conversion: {non_null_before} valeurs non nulles sur {len(merged_df)} lignes dans {col}")

                # Essayer de convertir avec plusieurs formats
                try:
                    # D'abord essayer la conversion standard
                    merged_df[col] = pd.to_datetime(merged_df[col], errors='coerce')

                    # Compter les valeurs non NaT après conversion standard
                    non_nat_after = merged_df[col].notna().sum()
                    logging.info(f"Après conversion standard: {non_nat_after} valeurs non NaT sur {len(merged_df)} lignes dans {col}")

                    # Si trop de NaT, essayer avec des formats spécifiques
                    if non_nat_after < non_null_before * 0.9:  # Si plus de 10% des valeurs non nulles sont devenues NaT
                        logging.warning(f"Perte de données dans {col}, essai avec formats spécifiques")

                        # Restaurer la colonne originale pour essayer d'autres formats
                        merged_df[col] = original_col

                        # Essayer avec différents formats courants
                        best_format = None
                        best_count = 0

                        for fmt in ['%d/%m/%Y %H:%M:%S', '%d/%m/%Y %H:%M', '%d-%m-%Y %H:%M:%S', '%Y-%m-%d %H:%M:%S', '%d/%m/%Y']:
                            try:
                                temp_col = pd.to_datetime(merged_df[col].astype(str), format=fmt, errors='coerce')
                                non_nat_count = temp_col.notna().sum()

                                if non_nat_count > best_count:
                                    best_count = non_nat_count
                                    best_format = fmt
                                    merged_df[col] = temp_col
                                    logging.info(f"Format {fmt} a converti {non_nat_count} valeurs pour {col}")
                            except Exception as e:
                                logging.debug(f"Erreur avec format {fmt}: {str(e)}")

                        if best_format:
                            logging.info(f"Meilleur format pour {col}: {best_format} avec {best_count} valeurs converties")
                        else:
                            # Si aucun format ne fonctionne mieux, restaurer les valeurs originales
                            merged_df[col] = original_col
                            logging.warning(f"Aucun format n'a amélioré la conversion pour {col}, restauration des valeurs originales")

                    # Vérifier les valeurs NaT après toutes les tentatives
                    final_nat_count = merged_df[col].isna().sum()
                    if final_nat_count > 0:
                        logging.warning(f"Après toutes les tentatives: {final_nat_count} valeurs NaT sur {len(merged_df)} lignes dans {col}")

                    # Afficher des statistiques après conversion
                    na_count = merged_df[col].isna().sum()
                    logging.info(f"Après conversion, {col} contient {na_count} valeurs NaT sur {len(merged_df)} lignes")
                    logging.info(f"Exemples de valeurs dans la colonne {col} après conversion: {merged_df[col].head(5).tolist()}")

                except Exception as e:
                    logging.error(f"Erreur lors de la conversion de {col}: {str(e)}")
            else:
                logging.warning(f"Colonne {col} non trouvée dans le dataframe")
        except Exception as e:
            logging.error(f"Erreur générale pour la colonne {col}: {str(e)}")
            logging.debug(traceback.format_exc())

    # Calculer le délai de résolution en jours décimaux (avec heures, minutes, secondes)
    delta = merged_df['Date résolution'] - merged_df['Date ouverture du cas']
    # Convertir les timedeltas en jours décimaux (1 jour = 86400 secondes)
    merged_df['Délai_résolution'] = delta.dt.total_seconds() / 86400
    # Arrondir à 2 décimales pour plus de lisibilité
    merged_df['Délai_résolution'] = merged_df['Délai_résolution'].round(2)

    # Conserver aussi le délai en jours entiers pour les conditions qui l'utilisent
    merged_df['Délai_résolution_jours'] = delta.dt.days

    merged_df['Catégorie'] = ''
    merged_df['Facturation éligible'] = 'NON'

    conditions = [
        (merged_df['Type'] == 'SIGNALISATION') & (merged_df['Diagnostic_OC'].str.startswith('RET', na=False)),
        (merged_df['Type'] == 'SIGNALISATION') & (merged_df['Diagnostic_OC'].str.startswith('STT', na=False)),
        (merged_df['Type'] == 'EXPERTISE') & (merged_df['Diagnostic_OC'].str.startswith('RET', na=False)),
        (merged_df['Type'] == 'EXPERTISE') & (merged_df['Diagnostic_OC'].str.startswith('STT', na=False)),
        (merged_df['Type'] == 'SIGNALISATION') & (merged_df['Diagnostic_OC'].str.startswith('ABS01', na=False) | merged_df['Diagnostic_OC'].str.startswith('ABS02', na=False)),
        (merged_df['Type'] == 'EXPERTISE') & (merged_df['Diagnostic_OC'].str.startswith('ABS01', na=False) | merged_df['Diagnostic_OC'].str.startswith('ABS02', na=False))
    ]
    categories = ['SIGRET', 'SIGSTT', 'EXPRET', 'EXPSTT', 'SIGABS', 'EXPABS']
    merged_df['Catégorie'] = np.select(conditions, categories, default='')

    # Utilisation de .loc pour éviter SettingWithCopyWarning
    merged_df.loc[(merged_df['Catégorie'] == 'SIGRET') & (merged_df['Délai_résolution_jours'] > 14), 'Facturation éligible'] = 'NON'
    merged_df.loc[merged_df['Catégorie'] == 'SIGSTT', 'Facturation éligible'] = 'OUI'
    merged_df.loc[merged_df['Catégorie'] == 'SIGABS', 'Facturation éligible'] = 'OUI'

    # Correction pour la catégorie SIGSTT
    merged_df.loc[
        (merged_df['Catégorie'] == 'SIGSTT') &
        (merged_df['Diagnostic_OC'].str.startswith('STT', na=False)) &  # Vérifie que c'est bien STT
        (merged_df['Ref_MUT'].notna()) &
        (merged_df.apply(lambda row: merged_df['Diagnostic_OC'][(merged_df['TT STIT'] == row['Ref_MUT'])].str.startswith('RET', na=False).any(), axis=1)),
        'Facturation éligible'
    ] = 'NON'  # Expertise RET existe, donc non éligible

    # Correction pour la catégorie SIGABS
    merged_df.loc[
        (merged_df['Catégorie'] == 'SIGABS') &
        ((merged_df['Diagnostic_OC'].str.startswith('ABS01', na=False)) | (merged_df['Diagnostic_OC'].str.startswith('ABS02', na=False))) &  # Vérifie que c'est bien ABS01 ou ABS02
        (merged_df['Ref_MUT'].notna()) &
        (merged_df.apply(lambda row: merged_df['Diagnostic_OC'][(merged_df['TT STIT'] == row['Ref_MUT'])].str.startswith('RET', na=False).any(), axis=1)),
        'Facturation éligible'
    ] = 'NON'  # Expertise RET existe, donc non éligible

    merged_df.loc[(merged_df['Catégorie'] == 'EXPRET') & (merged_df['Ref_MUT'].isna() | (merged_df['Ref_MUT'] == '')), 'Facturation éligible'] = 'NON'
    merged_df.loc[(merged_df['Catégorie'] == 'EXPSTT') & (merged_df['Ref_MUT'].isna() | (merged_df['Ref_MUT'] == '')), 'Facturation éligible'] = 'OUI'
    merged_df.loc[(merged_df['Catégorie'] == 'EXPSTT') & (~merged_df['Ref_MUT'].isna()) & (merged_df['Ref_MUT'] != ''), 'Facturation éligible'] = 'OUI'
    merged_df.loc[(merged_df['Catégorie'] == 'EXPABS') & (merged_df['Ref_MUT'].isna() | (merged_df['Ref_MUT'] == '')), 'Facturation éligible'] = 'OUI'
    merged_df.loc[(merged_df['Catégorie'] == 'EXPABS') & (~merged_df['Ref_MUT'].isna()) & (merged_df['Ref_MUT'] != ''), 'Facturation éligible'] = 'OUI'

    logging.info(f"Répartition des catégories: {merged_df['Catégorie'].value_counts().to_dict()}")
    logging.info(f"Nombre de cas éligibles: {(merged_df['Facturation éligible'] == 'OUI').sum()}")

    return merged_df

def create_summary_sheet_for_group(df, ws, oc, selected_month, start_row=1):
    """Crée la feuille de synthèse pour un groupe OI/OC.
       Prend en compte le mois sélectionné."""
    # Calculer les statistiques pour le groupe
    stats = {
        "Total des cas": len(df),
        "Cas éligibles": (df['Facturation éligible'] == 'OUI').sum(),
        "Pourcentage éligibilité": f"{((df['Facturation éligible'] == 'OUI').sum() / len(df) * 100):.2f}%" if len(df) > 0 else "0.00%",
        "Répartition par catégorie": df['Catégorie'].value_counts().to_dict()
    }

    # Ecriture du titre en A1
    title = f"Synthèse Facturation {oc} - Mois de résolution: {selected_month or 'Tous'}"  # Titre principal
    ws.cell(row=1, column=1, value=title)
    ws.cell(row=1, column=1).font = Font(bold=True, size=14)  # Gras et plus grand
    ws.cell(row=1, column=1).alignment = Alignment(horizontal='center')  # Centré
    ws.column_dimensions['A'].width = 90

   # Écrire les données dans la feuille de synthèse en A2
    for key, value in stats.items():
        ws.cell(row=start_row + 1, column=1, value=key)
        if isinstance(value, dict):
            # Écrire la répartition par catégorie
            col = 2
            for cat, count in value.items():
                ws.cell(row=start_row + 1, column=col, value=f"{cat}: {count}")
                col += 1
            start_row += 1  # On passe à la ligne suivante
        else:
            ws.cell(row=start_row + 1, column=2, value=str(value))
        start_row += 1 # Pour éviter l'écrasement on passe à la ligne suivante
    return start_row

def create_summary_sheet(result_path, df_final):
    """Crée une feuille de synthèse globale dans le fichier final, incluant des synthèses par OC_OI."""
    try:
        wb = openpyxl.load_workbook(result_path)
        # Création de la feuille de synthèse globale
        ws_synth = wb.create_sheet(title='Synthèse Globale')

        # Calcul des statistiques globales
        stats = {
            "Total des cas": len(df_final),
            "Cas éligibles": (df_final['Facturation éligible'] == 'OUI').sum(),
            "Pourcentage éligibilité": f"{((df_final['Facturation éligible'] == 'OUI').sum() / len(df_final) * 100):.2f}%" if len(df_final) > 0 else "0.00%",
            "Répartition par catégorie": df_final['Catégorie'].value_counts().to_dict()
        }
        # Écrire les données de synthèse
        row_start = 1

        # Ecriture du titre en A1
        title = f"Synthèse Facturation Globale"  # Titre principal
        ws_synth.cell(row=1, column=1, value=title)
        ws_synth.cell(row=1, column=1).font = Font(bold=True, size=14)  # Gras et plus grand
        ws_synth.cell(row=1, column=1).alignment = Alignment(horizontal='center')  # Centré
        ws_synth.column_dimensions['A'].width = 50  # Modifié à 50 au lieu de 90

        for key, value in stats.items():
            ws_synth.cell(row=row_start + 1, column=1, value=key)
            if isinstance(value, dict):
                # Écrire la répartition par catégorie
                col = 2
                for cat, count in value.items():
                    ws_synth.cell(row=row_start + 1, column=col, value=f"{cat}: {count}")
                    col += 1
                row_start += 1  # On passe à la ligne suivante

            else:
                ws_synth.cell(row=row_start + 1, column=2, value=str(value))
            row_start += 1

        # Ajouter un espacement
        row_start += 1

        # Ajouter les synthèses par OC_OI
        grouped = df_final.groupby(['OI', 'OC'])

        # Titre pour la section des synthèses par OC_OI
        section_title = "Synthèse par OC et OI"
        ws_synth.cell(row=row_start, column=1, value=section_title)
        ws_synth.cell(row=row_start, column=1).font = Font(bold=True, size=12)
        row_start += 2  # Espacement après le sous-titre

        # En-têtes des colonnes pour le tableau des synthèses OC_OI
        headers = ["OI", "OC", "Total des cas", "Cas éligibles", "Pourcentage éligibilité"]
        for col_idx, header in enumerate(headers, 1):
            ws_synth.cell(row=row_start, column=col_idx, value=header)
            ws_synth.cell(row=row_start, column=col_idx).font = Font(bold=True)
        row_start += 1

        # Pour chaque groupe OI/OC, ajouter une ligne de statistiques
        for (oi, oc), group in grouped:
            col = 1
            # OI
            ws_synth.cell(row=row_start, column=col, value=oi)
            col += 1

            # OC
            ws_synth.cell(row=row_start, column=col, value=oc)
            col += 1

            # Total des cas
            total_cases = len(group)
            ws_synth.cell(row=row_start, column=col, value=total_cases)
            col += 1

            # Cas éligibles
            eligible_cases = (group['Facturation éligible'] == 'OUI').sum()
            ws_synth.cell(row=row_start, column=col, value=eligible_cases)
            col += 1

            # Pourcentage éligibilité
            eligibility_percentage = f"{(eligible_cases / total_cases * 100):.2f}%" if total_cases > 0 else "0.00%"
            ws_synth.cell(row=row_start, column=col, value=eligibility_percentage)

            row_start += 1

        # Ajouter un espacement
        row_start += 2

        # Ajouter un tableau de répartition des catégories par OC_OI
        section_title = "Répartition des catégories par OC et OI"
        ws_synth.cell(row=row_start, column=1, value=section_title)
        ws_synth.cell(row=row_start, column=1).font = Font(bold=True, size=12)
        row_start += 2  # Espacement après le sous-titre

        # Récupérer toutes les catégories uniques
        all_categories = sorted(df_final['Catégorie'].unique())

        # En-têtes des colonnes pour le tableau des catégories
        cat_headers = ["OI", "OC"] + list(all_categories)
        for col_idx, header in enumerate(cat_headers, 1):
            ws_synth.cell(row=row_start, column=col_idx, value=header)
            ws_synth.cell(row=row_start, column=col_idx).font = Font(bold=True)
        row_start += 1

        # Pour chaque groupe OI/OC, ajouter une ligne avec la répartition des catégories
        for (oi, oc), group in grouped:
            col = 1
            # OI
            ws_synth.cell(row=row_start, column=col, value=oi)
            col += 1

            # OC
            ws_synth.cell(row=row_start, column=col, value=oc)
            col += 1

            # Comptage par catégorie
            cat_counts = group['Catégorie'].value_counts().to_dict()

            # Remplir les valeurs pour chaque catégorie
            for cat in all_categories:
                count = cat_counts.get(cat, 0)
                ws_synth.cell(row=row_start, column=col, value=count)
                col += 1

            row_start += 1

        # Formatage:
        for row in ws_synth.iter_rows():
            for cell in row:
                cell.number_format = '@'
        ws_synth.sheet_view.showGridLines = False

        # Réglage de la largeur des colonnes - Modifié selon la demande
        ws_synth.column_dimensions['A'].width = 90  # Changé à 80
        ws_synth.column_dimensions['B'].width = 25  # Changé à 25
        ws_synth.column_dimensions['C'].width = 25  # Changé à 25
        ws_synth.column_dimensions['D'].width = 25  # Changé à 25
        ws_synth.column_dimensions['E'].width = 25  # Changé à 25

        # Pour les autres colonnes, on garde une largeur standard
        for col_idx in range(6, ws_synth.max_column + 1):
            col_letter = get_column_letter(col_idx)
            ws_synth.column_dimensions[col_letter].width = 20

        wb.save(result_path)
        logging.info("Feuille de synthèse globale créée avec succès, incluant les synthèses par OC_OI.")

    except Exception as e:
        logging.error(f"ERREUR création synthèse globale: {str(e)}")
        logging.debug(traceback.format_exc())

def split_final_file(result_path, output_dir, selected_month=None):
    """Découpe le fichier final par OI et OC et inclut la feuille de synthèse correspondante."""
    try:
        df = pd.read_excel(result_path, engine='openpyxl')
        # Chargement du workbook principal n'est pas nécessaire ici

        if selected_month:
            output_dir = output_dir / str(selected_month)
        output_dir.mkdir(parents=True, exist_ok=True)

        xpf_dir = output_dir / 'XPF'
        sfra_dir = output_dir / 'SFRA'
        xpf_dir.mkdir(parents=True, exist_ok=True)
        sfra_dir.mkdir(parents=True, exist_ok=True)

        grouped = df.groupby(['OI', 'OC'])

        for (oi, oc), group in grouped:
            target_dir = xpf_dir if oi == 'XPF' else sfra_dir if oi == 'SFRA' else None
            if not target_dir:
                continue

            group_clean = sanitize_excel_output(group)
            # Extraire le nom de base du fichier (peut contenir le mois de résolution)
            base_name = result_path.stem
            safe_oc = sanitize_filename(oc)
            safe_oi = sanitize_filename(oi)
            new_name = f"{base_name}_{safe_oc}_{safe_oi}.xlsx"
            new_path = target_dir / new_name

            # Création d'un nouveau workbook
            wb_new = openpyxl.Workbook()
            ws_new = wb_new.active
            ws_new.title = 'Data'

            # Convertir les colonnes de date en string si elles sont encore en datetime
            group_for_export = group_clean.copy()

            # Remplacer tous les NaN par des chaînes vides pour éviter d'avoir "nan" dans les fichiers découpés
            for col in group_for_export.columns:
                if group_for_export[col].dtype == 'object' or pd.api.types.is_string_dtype(group_for_export[col]):
                    group_for_export[col] = group_for_export[col].fillna('')

            # Traitement spécial pour les colonnes de date
            for col in ['Date ouverture du cas', 'Date résolution']:
                if col in group_for_export.columns:
                    # Vérifier si la colonne est de type datetime
                    if pd.api.types.is_datetime64_any_dtype(group_for_export[col]):
                        # Compter les valeurs NaT avant conversion
                        nat_count = group_for_export[col].isna().sum()
                        logging.info(f"Fichier découpé: Avant conversion en string, {col} contient {nat_count} valeurs NaT sur {len(group_for_export)} lignes")

                        # Convertir les dates valides au format JJ/MM/AAAA HH:MM:SS
                        group_for_export[col] = group_for_export[col].dt.strftime('%d/%m/%Y %H:%M:%S')

                        # Remplacer les NaN (qui étaient des NaT) par une chaîne vide
                        group_for_export[col] = group_for_export[col].fillna('')
                    else:
                        # Si la colonne n'est pas de type datetime, s'assurer qu'elle ne contient pas de valeurs nulles
                        group_for_export[col] = group_for_export[col].fillna('')

                    # Vérifier les valeurs vides après traitement
                    empty_count = (group_for_export[col] == '').sum()
                    logging.info(f"Fichier découpé: Après traitement, {col} contient {empty_count} valeurs vides sur {len(group_for_export)} lignes")

            # Traitement spécial pour Délai_résolution - remplacer les points par des virgules
            if 'Délai_résolution' in group_for_export.columns:
                # Convertir en string avec virgule au lieu de point
                group_for_export['Délai_résolution'] = group_for_export['Délai_résolution'].astype(str).str.replace('.', ',', regex=False)
                logging.info("Délai_résolution formaté avec virgules pour le fichier découpé")

            # Vérifier et corriger les incohérences entre Code OI InterOP et OI
            if 'Code OI InterOP' in group_for_export.columns and 'OI' in group_for_export.columns:
                # Cas spécifique : Code OI InterOP = SFRA doit avoir OI = SFRA
                sfra_mask = (group_for_export['Code OI InterOP'] == 'SFRA') & (group_for_export['OI'] != 'SFRA')
                sfra_count = sfra_mask.sum()

                if sfra_count > 0:
                    # Corriger les valeurs
                    group_for_export.loc[sfra_mask, 'OI'] = 'SFRA'
                    logging.info(f"Corrigé {sfra_count} lignes dans le fichier découpé pour que OI = SFRA lorsque Code OI InterOP = SFRA")

            # Vérification pour s'assurer qu'il ne reste que des tickets avec le statut "Clos"
            if 'Statut' in group_for_export.columns:
                # Nettoyer la colonne Statut (supprimer les espaces, normaliser la casse)
                group_for_export['Statut'] = group_for_export['Statut'].astype(str).str.strip().str.title()

                # Vérifier s'il reste des tickets avec un statut autre que "Clos"
                non_clos_mask = ~group_for_export['Statut'].str.contains('Clos', case=False, na=False)
                non_clos_count = non_clos_mask.sum()

                if non_clos_count > 0:
                    # Supprimer ces lignes
                    group_for_export = group_for_export[~non_clos_mask].copy()
                    logging.info(f"Fichier découpé: Supprimé {non_clos_count} lignes avec un statut autre que 'Clos'")

            # Traitement spécial pour DEPT - s'assurer qu'il est en format texte sans décimales et sans 'nan'
            if 'DEPT' in group_for_export.columns:
                # Vérifier les valeurs NaN avant le formatage
                nan_count = group_for_export['DEPT'].isna().sum()
                if nan_count > 0:
                    logging.warning(f"Trouvé {nan_count} valeurs NaN dans la colonne DEPT avant l'export du fichier découpé")

                # Remplacer les NaN par une chaîne vide
                group_for_export['DEPT'] = group_for_export['DEPT'].fillna('')

                # Convertir en string et supprimer les décimales (.0)
                group_for_export['DEPT'] = group_for_export['DEPT'].astype(str).replace(r'\.0$', '', regex=True)

                # Remplacer 'nan' par une chaîne vide
                group_for_export['DEPT'] = group_for_export['DEPT'].replace('nan', '')

                # Vérification finale et tentative de correction des DEPT vides
                dept_empty_count = (group_for_export['DEPT'] == '').sum()
                if dept_empty_count > 0:
                    logging.warning(f"Il reste {dept_empty_count} DEPT vides avant l'export du fichier découpé")

                    # Essayer de compléter les DEPT vides en utilisant le PM
                    pm_to_dept = {}
                    for idx, row in group_for_export.iterrows():
                        if row['PM'] and row['DEPT'] and row['DEPT'] != '':
                            pm_to_dept[row['PM']] = row['DEPT']

                    # Appliquer le dictionnaire pour compléter les valeurs manquantes
                    dept_filled = 0
                    for idx, row in group_for_export[group_for_export['DEPT'] == ''].iterrows():
                        if row['PM'] and row['PM'] in pm_to_dept:
                            group_for_export.at[idx, 'DEPT'] = pm_to_dept[row['PM']]
                            dept_filled += 1

                    if dept_filled > 0:
                        logging.info(f"DEPT complété pour {dept_filled} lignes supplémentaires dans le fichier découpé")

                logging.info("DEPT formaté sans décimales et sans 'nan' pour le fichier découpé")
                logging.info(f"Exemples de valeurs DEPT avant export du fichier découpé: {group_for_export['DEPT'].head(5).tolist()}")

            # Écriture des données avec traitement spécial pour les cellules vides
            # D'abord écrire les en-têtes
            headers = list(group_for_export.columns)
            ws_new.append(headers)

            # Ensuite écrire les données ligne par ligne
            for _, row in group_for_export.iterrows():
                row_values = []
                for col in headers:
                    # Convertir les NaN et 'nan' en None pour qu'ils apparaissent comme des cellules vides
                    value = row[col]
                    if pd.isna(value) or value == 'nan' or value == '':
                        row_values.append(None)  # None sera interprété comme une cellule vide par openpyxl
                    else:
                        row_values.append(value)
                ws_new.append(row_values)

            # Formatage :
            date_columns = []
            # Identifier les colonnes de date par leur nom
            for i, cell in enumerate(ws_new[1]):
                if cell.value in ['Date ouverture du cas', 'Date résolution']:
                    date_columns.append(i+1)  # +1 car les colonnes Excel commencent à 1

            # Identifier les colonnes spéciales pour le formatage
            delai_column = None
            dept_column = None
            for i, cell in enumerate(ws_new[1]):
                if cell.value == 'Délai_résolution':
                    delai_column = i+1  # +1 car les colonnes Excel commencent à 1
                elif cell.value == 'DEPT':
                    dept_column = i+1
                    break

            # Appliquer le formatage approprié à chaque cellule
            for row in ws_new.iter_rows(min_row=2):  # Commencer à la ligne 2 (après les en-têtes)
                for i, cell in enumerate(row, 1):  # i commence à 1 pour correspondre aux indices de colonnes Excel
                    if i in date_columns:
                        # Format de date français pour les colonnes de date
                        if cell.value and str(cell.value).strip():
                            cell.number_format = 'dd/mm/yyyy hh:mm:ss'
                    elif i == delai_column:
                        # Format décimal pour la colonne Délai_résolution avec virgule
                        if cell.value is not None:
                            # Appliquer un format texte pour conserver la virgule
                            cell.number_format = '@'
                    elif i == dept_column:
                        # Format texte pour la colonne DEPT
                        cell.number_format = '@'
                    else:
                        # Format texte pour les autres colonnes
                        cell.number_format = '@'

            # Ajuster la largeur des colonnes
            for i, column in enumerate(ws_new.columns):
                max_length = 0
                column_letter = openpyxl.utils.get_column_letter(i + 1)
                for cell in column:
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                adjusted_width = (max_length + 2) * 1.2
                ws_new.column_dimensions[column_letter].width = min(adjusted_width, 50)  # Limiter à 50 pour éviter des colonnes trop larges

            ws_new.sheet_view.showGridLines = False

            # Création de la feuille de synthèse par OI/OC
            sheet_name = 'Synthèse Facturation'
            ws_synth = wb_new.create_sheet(title=sheet_name)  # Créer la feuille dans chaque fichier

            # Calcul et écriture des statistiques pour ce groupe
            row_start = 1
            row_start = create_summary_sheet_for_group(group, ws_synth, oc, selected_month, row_start)  # On passe OC et selected_month

            # Réglage de la largeur des colonnes
            for col in "A":  # Colonnes à ajuster
                ws_synth.column_dimensions[col].width = 80

            for col in "BCDE":  # Colonnes à ajuster
                ws_synth.column_dimensions[col].width = 30  # Ajuste la largeur


            # Formatage de la feuille de synthèse
            for row in ws_synth.iter_rows():
                for cell in row:
                    cell.number_format = '@'
            ws_synth.sheet_view.showGridLines = False

            # Sauvegarde
            wb_new.save(new_path)
            logging.info(f"Fichier créé : {new_path}")
        logging.info("Découpage terminé avec succès.")
        return True

    except Exception as e:
        logging.error(f"ERREUR découpage : {str(e)}")
        logging.debug(traceback.format_exc())
        return False

def get_selected_month(reporting_file):
    """
    Permet à l'utilisateur de sélectionner un mois parmi ceux disponibles.
    Utilise une approche simple de sélection par numéro.
    """
    try:
        df = pd.read_excel(reporting_file, engine='openpyxl')
        df['Mois_Résolution'] = df['Mois_Résolution'].astype(str)
        available_months = sorted(df['Mois_Résolution'].unique().tolist())

        if not available_months:
            logging.warning("Aucun mois de résolution trouvé dans le fichier de reporting.")
            return None

        # Afficher les mois disponibles avec des numéros
        print("\nMois de résolution disponibles :")
        for i, month in enumerate(available_months, 1):
            print(f"{i}. {month}")
        print("0. Tous les mois")

        while True:
            try:
                choice = input("\nEntrez le numéro du mois à sélectionner (0 pour tous) : ").strip()

                # Convertir l'entrée en entier
                choice_num = int(choice)

                # Vérifier la validité du choix
                if choice_num == 0:
                    return None  # Tous les mois
                elif 1 <= choice_num <= len(available_months):
                    return available_months[choice_num - 1]
                else:
                    print("Choix invalide. Veuillez entrer un numéro valide.")

            except ValueError:
                print("Veuillez entrer un nombre valide.")

    except Exception as e:
        logging.error(f"Erreur lors de la sélection du mois : {e}")
        return None

def main():
    base_path = Path(r"C:\Users\<USER>\Desktop\KPI")
    today_str = datetime.date.today().strftime("%Y_%m_%d")
    output_dir = base_path / "Facturation_STT" / today_str
    ensure_directory_exists(output_dir)
    log_file = output_dir / "kpi_processing.log"
    setup_logging(log_file)
    logging.info(f"Début traitement - {today_str}")
    src_files = {
        "extract": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\_FTTH_TT_STIT.xlsx"),
        "reporting": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\FTTH - Reporting cas STC_REFMUT_ABS.xlsx"),
        "sadira": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\FTTH_Liste_PM_source_sadira.xlsx"),
        "oi": Path(r"U:\Lyon\STC Rési Bron\ISABEL\FTTH\Reporting\Liste_OI.xlsx"),
        "manquant_ref_mut": Path(r"U:\Lyon\STC Rési Bron\ISABEL\FTTH\Reporting\Base_manquant_REF_MUT.xlsx"),
        "analyse_stit": Path(r"U:\Lyon\STC Rési Bron\ISABEL\FTTH\Reporting\Analyse_STIT_manquant.xlsx")
    }

    # Modifier copy_file pour ne copier que si le fichier source est plus récent que la destination
    try:
        dest_files = {key: output_dir / src_files[key].name for key in src_files}
        # Avant de copier, on vérifie la date de modification du fichier source et de la destination
        for key in src_files:
            copy_file(src_files[key], dest_files[key])

        df_merged = merge_datasets(
            dest_files["extract"],
            dest_files["reporting"],
            dest_files["sadira"],
            dest_files["oi"]
        )

        # Demander à l'utilisateur de choisir le mois
        selected_month = get_selected_month(dest_files["reporting"])

        # Appliquer le filtre Mois_Résolution avant le calcul
        df_final = determine_facturation_eligibility(df_merged, selected_month)

        # Compléter les informations manquantes avec le fichier Base_manquant_REF_MUT.xlsx
        logging.info("Vérification et complétion des données manquantes avec Base_manquant_REF_MUT.xlsx...")
        try:
            # Charger le fichier de référence
            df_manquant_ref_mut = pd.read_excel(dest_files["manquant_ref_mut"], engine='openpyxl')
            df_manquant_ref_mut.columns = df_manquant_ref_mut.columns.astype(str).str.strip()

            # Afficher les colonnes trouvées pour le débogage
            logging.info(f"Colonnes trouvées dans Base_manquant_REF_MUT.xlsx: {df_manquant_ref_mut.columns.tolist()}")

            # Vérifier si les colonnes nécessaires existent - adaptation à la structure réelle du fichier
            required_columns = ['Ref_MUT', 'PM', 'DEPT', 'Code OI InterOP']
            if all(col in df_manquant_ref_mut.columns for col in required_columns):
                # Compteurs pour les statistiques
                pm_completed_count = 0
                dept_completed_count = 0
                code_oi_updated_count = 0
                oi_missing_count = 0

                # Compter les DEPT vides avant complétion
                dept_empty_before = (df_final['DEPT'].isna() | (df_final['DEPT'] == '')).sum()
                logging.info(f"Avant complétion avec Base_manquant_REF_MUT.xlsx: {dept_empty_before} DEPT vides")

                # Parcourir toutes les lignes du dataframe final
                for idx, row in df_final.iterrows():
                    ref_mut_value = row['Ref_MUT'] if 'Ref_MUT' in row else None
                    if pd.notna(ref_mut_value) and ref_mut_value != '':
                        # Chercher dans df_manquant_ref_mut les lignes correspondant à la Ref_MUT
                        matching_rows = df_manquant_ref_mut[df_manquant_ref_mut['Ref_MUT'] == ref_mut_value]
                        if not matching_rows.empty:
                            # Prendre la première correspondance
                            match = matching_rows.iloc[0]

                            # 1. Si PM est vide, le compléter avec la valeur du fichier de référence
                            if pd.isna(row['PM']) or row['PM'] == '':
                                if pd.notna(match['PM']) and match['PM'] != '':
                                    df_final.at[idx, 'PM'] = match['PM']
                                    pm_completed_count += 1
                                    logging.info(f"PM complété pour Ref_MUT: {ref_mut_value}")

                            # 2. Si DEPT est vide, le compléter avec la valeur du fichier de référence
                            if pd.isna(row['DEPT']) or row['DEPT'] == '' or row['DEPT'] == 'nan':
                                if pd.notna(match['DEPT']) and match['DEPT'] != '' and match['DEPT'] != 'nan':
                                    # S'assurer que DEPT est en format texte sans décimales
                                    dept_value = str(match['DEPT']).replace('.0', '')
                                    df_final.at[idx, 'DEPT'] = dept_value
                                    dept_completed_count += 1
                                    logging.info(f"DEPT complété pour Ref_MUT: {ref_mut_value} avec valeur: {dept_value}")

                            # 3. Toujours écraser Code OI InterOP avec la valeur du fichier de référence
                            if pd.notna(match['Code OI InterOP']) and match['Code OI InterOP'] != '':
                                df_final.at[idx, 'Code OI InterOP'] = match['Code OI InterOP']
                                code_oi_updated_count += 1
                                logging.info(f"Code OI InterOP mis à jour pour Ref_MUT: {ref_mut_value}")

                            # 4. Vérifier si OI est manquant
                            if pd.isna(row['OI']) or row['OI'] == '':
                                oi_missing_count += 1

                # Maintenant, utiliser le fichier Liste_OI.xlsx pour compléter les OI manquants
                try:
                    # Recharger Liste_OI pour être sûr d'avoir les données les plus à jour
                    df_oi = pd.read_excel(dest_files["oi"], engine='openpyxl')
                    df_oi.columns = df_oi.columns.astype(str).str.strip()

                    # Renommer pour correspondre à nos colonnes
                    df_oi.rename(columns={
                        'Propriétaire - LTDET': 'Propriétaire',
                        'OI': 'OI'
                    }, inplace=True)

                    # Pour chaque ligne où OI est vide mais Code OI InterOP existe
                    missing_oi_with_code = (df_final['OI'].isna() | (df_final['OI'] == '')) & (df_final['Code OI InterOP'].notna() & (df_final['Code OI InterOP'] != ''))
                    oi_fill_count = 0

                    for idx, row in df_final[missing_oi_with_code].iterrows():
                        code_oi = row['Code OI InterOP']
                        matching_oi_rows = df_oi[df_oi['Code OI InterOP'] == code_oi]

                        if not matching_oi_rows.empty:
                            df_final.at[idx, 'OI'] = matching_oi_rows.iloc[0]['OI']
                            oi_fill_count += 1

                    logging.info(f"OI complété pour {oi_fill_count} lignes à partir de Liste_OI.xlsx")

                    # Vérifier et corriger les incohérences entre Code OI InterOP et OI
                    # Cas spécifique : Code OI InterOP = SFRA doit avoir OI = SFRA
                    sfra_mask = (df_final['Code OI InterOP'] == 'SFRA') & (df_final['OI'] != 'SFRA')
                    sfra_count = sfra_mask.sum()

                    if sfra_count > 0:
                        # Afficher les valeurs incohérentes pour débogage
                        logging.warning(f"Trouvé {sfra_count} lignes avec Code OI InterOP = SFRA mais OI != SFRA")
                        for idx, row in df_final[sfra_mask].head(5).iterrows():
                            logging.warning(f"Ligne {idx}: Code OI InterOP = {row['Code OI InterOP']}, OI = {row['OI']}")

                        # Corriger les valeurs
                        df_final.loc[sfra_mask, 'OI'] = 'SFRA'
                        logging.info(f"Corrigé {sfra_count} lignes pour que OI = SFRA lorsque Code OI InterOP = SFRA")

                    # Vérifier s'il y a d'autres incohérences entre Code OI InterOP et OI
                    # Créer un dictionnaire de correspondance à partir de Liste_OI.xlsx
                    code_to_oi = {}
                    for _, row in df_oi.iterrows():
                        if pd.notna(row['Code OI InterOP']) and pd.notna(row['OI']):
                            code_to_oi[row['Code OI InterOP']] = row['OI']

                    # Identifier les lignes où Code OI InterOP et OI ne correspondent pas selon Liste_OI.xlsx
                    mismatch_count = 0
                    for idx, row in df_final.iterrows():
                        if pd.notna(row['Code OI InterOP']) and row['Code OI InterOP'] in code_to_oi:
                            expected_oi = code_to_oi[row['Code OI InterOP']]
                            if pd.notna(row['OI']) and row['OI'] != expected_oi:
                                df_final.at[idx, 'OI'] = expected_oi
                                mismatch_count += 1

                    if mismatch_count > 0:
                        logging.info(f"Corrigé {mismatch_count} autres incohérences entre Code OI InterOP et OI")

                except Exception as e:
                    logging.error(f"Erreur lors de la complétion des OI avec Liste_OI.xlsx: {str(e)}")

                # Résumé des modifications effectuées
                logging.info(f"Résumé des modifications:")
                logging.info(f"- PM complétés: {pm_completed_count}")
                logging.info(f"- DEPT complétés: {dept_completed_count}")
                logging.info(f"- Code OI InterOP mis à jour: {code_oi_updated_count}")
                logging.info(f"- Lignes avec OI manquant: {oi_missing_count}")

                # Supprimer les lignes où "Code OI InterOP" contient "SRRA"
                if 'Code OI InterOP' in df_final.columns:
                    # Identifier les lignes où "Code OI InterOP" contient "SRRA"
                    srra_mask = df_final['Code OI InterOP'].astype(str).str.contains('SRRA', na=False)
                    srra_count = srra_mask.sum()

                    # Supprimer ces lignes
                    if srra_count > 0:
                        df_final = df_final[~srra_mask]
                        logging.info(f"Suppression de {srra_count} lignes où 'Code OI InterOP' contient 'SRRA' après complétion des données")
                    else:
                        logging.info("Aucune ligne avec 'SRRA' dans 'Code OI InterOP' n'a été trouvée après complétion des données")

                # NOUVELLE APPROCHE: Compléter les informations manquantes de STIT et TT STIT
                try:
                    logging.info("=== NOUVELLE APPROCHE: Complétion des informations manquantes de STIT et TT STIT ===")

                    # 1. Charger le fichier Analyse_STIT_manquant.xlsx, onglet TT_STIT
                    logging.info("1. Chargement du fichier Analyse_STIT_manquant.xlsx, onglet TT_STIT")
                    df_analyse_stit = pd.read_excel(dest_files["analyse_stit"], sheet_name="TT_STIT", engine='openpyxl')
                    df_analyse_stit.columns = df_analyse_stit.columns.astype(str).str.strip()
                    logging.info(f"Colonnes trouvées dans l'onglet TT_STIT: {df_analyse_stit.columns.tolist()}")

                    # 2. Charger le fichier _FTTH_TT_STIT.xlsx
                    logging.info("2. Chargement du fichier _FTTH_TT_STIT.xlsx")
                    df_tt_stit = pd.read_excel(dest_files["extract"], engine='openpyxl')
                    df_tt_stit.columns = df_tt_stit.columns.astype(str).str.strip()
                    logging.info(f"Colonnes trouvées dans _FTTH_TT_STIT.xlsx: {df_tt_stit.columns.tolist()}")

                    # Afficher les premières lignes pour débogage
                    if not df_analyse_stit.empty:
                        logging.info(f"Premières lignes de l'onglet TT_STIT:\n{df_analyse_stit.head().to_string()}")
                    if not df_tt_stit.empty:
                        logging.info(f"Premières lignes de _FTTH_TT_STIT.xlsx:\n{df_tt_stit.head().to_string()}")

                    # 3. Nettoyer et préparer les données
                    logging.info("3. Nettoyage et préparation des données")

                    # Nettoyer les données de Analyse_STIT_manquant.xlsx
                    for col in df_analyse_stit.columns:
                        if df_analyse_stit[col].dtype == 'object':
                            df_analyse_stit[col] = df_analyse_stit[col].astype(str).str.strip()

                    # Nettoyer les données de _FTTH_TT_STIT.xlsx
                    for col in df_tt_stit.columns:
                        if df_tt_stit[col].dtype == 'object':
                            df_tt_stit[col] = df_tt_stit[col].astype(str).str.strip()

                    # Compteurs pour les statistiques
                    stit_completed_count = 0
                    tt_stit_completed_count = 0

                    # 4. Identifier les colonnes dans les fichiers
                    logging.info("4. Identification des colonnes dans les fichiers")

                    # Définir les colonnes requises et leurs alternatives possibles
                    analyse_stit_columns = {
                        'id_cas': None,  # Colonne pour Identifiant cas
                        'tt_stit': None  # Colonne pour TT STIT
                    }

                    tt_stit_columns = {
                        'tt_stit': None,  # Colonne pour TT STIT
                        'stit': None      # Colonne pour Sous-traitant intervention
                    }

                    # Recherche des colonnes dans Analyse_STIT_manquant.xlsx
                    id_cas_alternatives = ['Identifiant cas', 'Identifiant_cas', 'ID_cas', 'ID cas', 'Identifiant']
                    for alt in id_cas_alternatives:
                        if alt in df_analyse_stit.columns:
                            analyse_stit_columns['id_cas'] = alt
                            break

                    tt_stit_alternatives = ['TT_STIT', 'TT STIT', 'TTSTIT', 'TT-STIT', 'Ticket STIT']
                    for alt in tt_stit_alternatives:
                        if alt in df_analyse_stit.columns:
                            analyse_stit_columns['tt_stit'] = alt
                            break

                    # Recherche des colonnes dans _FTTH_TT_STIT.xlsx
                    for alt in tt_stit_alternatives:
                        if alt in df_tt_stit.columns:
                            tt_stit_columns['tt_stit'] = alt
                            break

                    stit_alternatives = ['Sous-traitant intervention', 'Sous-traitant', 'STIT', 'Sous_traitant', 'Sous-traitant_intervention']
                    for alt in stit_alternatives:
                        if alt in df_tt_stit.columns:
                            tt_stit_columns['stit'] = alt
                            break

                    # Afficher les colonnes identifiées
                    logging.info(f"Colonnes identifiées dans Analyse_STIT_manquant.xlsx: {analyse_stit_columns}")
                    logging.info(f"Colonnes identifiées dans _FTTH_TT_STIT.xlsx: {tt_stit_columns}")

                    # 5. Vérifier si toutes les colonnes nécessaires ont été trouvées
                    logging.info("5. Vérification des colonnes nécessaires")

                    missing_columns = []
                    if analyse_stit_columns['id_cas'] is None:
                        missing_columns.append("Identifiant cas dans Analyse_STIT_manquant.xlsx")
                    if analyse_stit_columns['tt_stit'] is None:
                        missing_columns.append("TT STIT dans Analyse_STIT_manquant.xlsx")
                    if tt_stit_columns['tt_stit'] is None:
                        missing_columns.append("TT STIT dans _FTTH_TT_STIT.xlsx")
                    if tt_stit_columns['stit'] is None:
                        missing_columns.append("Sous-traitant intervention dans _FTTH_TT_STIT.xlsx")

                    if not missing_columns:
                        logging.info("Toutes les colonnes nécessaires ont été trouvées")

                        # 6. Préparer les données pour la jointure
                        logging.info("6. Préparation des données pour la jointure")

                        # Extraire les noms des colonnes identifiées
                        id_col = analyse_stit_columns['id_cas']
                        tt_stit_col_analyse = analyse_stit_columns['tt_stit']
                        tt_stit_col_extract = tt_stit_columns['tt_stit']
                        stit_col = tt_stit_columns['stit']

                        # Filtrer les lignes avec des valeurs valides dans Analyse_STIT_manquant.xlsx
                        df_analyse_stit_filtered = df_analyse_stit[
                            df_analyse_stit[id_col].notna() &
                            (df_analyse_stit[id_col] != '') &
                            df_analyse_stit[tt_stit_col_analyse].notna() &
                            (df_analyse_stit[tt_stit_col_analyse] != '')
                        ].copy()

                        # Filtrer les lignes avec des valeurs valides dans _FTTH_TT_STIT.xlsx
                        df_tt_stit_filtered = df_tt_stit[
                            df_tt_stit[tt_stit_col_extract].notna() &
                            (df_tt_stit[tt_stit_col_extract] != '') &
                            df_tt_stit[stit_col].notna() &
                            (df_tt_stit[stit_col] != '')
                        ].copy()

                        # Renommer les colonnes pour faciliter la jointure
                        df_analyse_stit_filtered = df_analyse_stit_filtered.rename(columns={
                            id_col: 'Identifiant_cas',
                            tt_stit_col_analyse: 'TT_STIT_value'
                        })

                        df_tt_stit_filtered = df_tt_stit_filtered.rename(columns={
                            tt_stit_col_extract: 'TT_STIT_value',
                            stit_col: 'Sous_traitant_intervention'
                        })

                        logging.info(f"Nombre de lignes dans df_analyse_stit_filtered: {len(df_analyse_stit_filtered)}")
                        logging.info(f"Nombre de lignes dans df_tt_stit_filtered: {len(df_tt_stit_filtered)}")

                        # 7. Joindre les données de Analyse_STIT_manquant.xlsx et _FTTH_TT_STIT.xlsx
                        logging.info("7. Jointure des données de Analyse_STIT_manquant.xlsx et _FTTH_TT_STIT.xlsx")

                        # Joindre df_analyse_stit_filtered et df_tt_stit_filtered sur TT_STIT_value
                        df_joined = pd.merge(df_analyse_stit_filtered, df_tt_stit_filtered, on='TT_STIT_value', how='left')
                        logging.info(f"Nombre de lignes après jointure des deux fichiers: {len(df_joined)}")

                        # 8. Compléter les informations dans df_final
                        logging.info("8. Complétion des informations dans le dataframe final")

                        if 'Identifiant cas' in df_final.columns:
                            # Convertir la colonne Identifiant cas en string pour la jointure
                            df_final['Identifiant cas'] = df_final['Identifiant cas'].astype(str).str.strip()

                            # Compter les lignes avec TT STIT et STIT manquants avant la jointure
                            missing_tt_stit_before = (df_final['TT STIT'].isna() | (df_final['TT STIT'] == '')).sum()
                            missing_stit_before = (df_final['STIT'].isna() | (df_final['STIT'] == '')).sum()
                            logging.info(f"Avant la jointure: {missing_tt_stit_before} lignes avec TT STIT manquant et {missing_stit_before} lignes avec STIT manquant")

                            # Créer un DataFrame temporaire pour la jointure avec df_final
                            temp_df = df_joined[['Identifiant_cas', 'TT_STIT_value', 'Sous_traitant_intervention']].copy()
                            temp_df = temp_df.rename(columns={
                                'Identifiant_cas': 'Identifiant cas',
                                'TT_STIT_value': 'TT_STIT_new',
                                'Sous_traitant_intervention': 'STIT_new'
                            })

                            # Faire la jointure sur Identifiant cas
                            df_final = pd.merge(df_final, temp_df, on='Identifiant cas', how='left')
                            logging.info(f"Nombre de lignes dans df_final après jointure: {len(df_final)}")

                            # Compléter TT STIT là où il est vide
                            mask_tt_stit = (df_final['TT STIT'].isna() | (df_final['TT STIT'] == '')) & df_final['TT_STIT_new'].notna() & (df_final['TT_STIT_new'] != '')
                            tt_stit_to_update = mask_tt_stit.sum()

                            if tt_stit_to_update > 0:
                                df_final.loc[mask_tt_stit, 'TT STIT'] = df_final.loc[mask_tt_stit, 'TT_STIT_new']
                                logging.info(f"TT STIT mis à jour pour {tt_stit_to_update} lignes")

                            # Compléter STIT là où il est vide
                            mask_stit = (df_final['STIT'].isna() | (df_final['STIT'] == '')) & df_final['STIT_new'].notna() & (df_final['STIT_new'] != '')
                            stit_to_update = mask_stit.sum()

                            if stit_to_update > 0:
                                df_final.loc[mask_stit, 'STIT'] = df_final.loc[mask_stit, 'STIT_new']
                                logging.info(f"STIT mis à jour pour {stit_to_update} lignes")

                            # Supprimer les colonnes temporaires
                            df_final.drop(columns=['TT_STIT_new', 'STIT_new'], inplace=True, errors='ignore')

                            # Compter les lignes avec TT STIT et STIT manquants après la jointure
                            missing_tt_stit_after = (df_final['TT STIT'].isna() | (df_final['TT STIT'] == '')).sum()
                            missing_stit_after = (df_final['STIT'].isna() | (df_final['STIT'] == '')).sum()

                            # Calculer combien de valeurs ont été complétées
                            tt_stit_completed_count = missing_tt_stit_before - missing_tt_stit_after
                            stit_completed_count = missing_stit_before - missing_stit_after

                            logging.info(f"TT STIT complété pour {tt_stit_completed_count} lignes")
                            logging.info(f"STIT complété pour {stit_completed_count} lignes")
                            logging.info(f"Après la jointure: {missing_tt_stit_after} lignes avec TT STIT manquant et {missing_stit_after} lignes avec STIT manquant")
                        else:
                            logging.warning("Colonne 'Identifiant cas' non trouvée dans le dataframe final, impossible de faire la jointure")
                    else:
                        logging.warning(f"Colonnes manquantes: {missing_columns}")
                        logging.warning("Impossible de compléter les informations manquantes de STIT et TT STIT")

                    # 9. Traiter les cas où il reste des valeurs manquantes
                    logging.info("9. Traitement des cas où il reste des valeurs manquantes")

                    if 'TT STIT' in df_final.columns and 'STIT' in df_final.columns:
                        # Vérifier s'il reste des lignes avec TT STIT manquant
                        missing_tt_stit_mask = (df_final['TT STIT'].isna() | (df_final['TT STIT'] == ''))
                        missing_tt_stit_count = missing_tt_stit_mask.sum()

                        if missing_tt_stit_count > 0:
                            logging.info(f"Il reste {missing_tt_stit_count} lignes avec TT STIT manquant")

                        # Vérifier s'il reste des lignes avec STIT manquant mais TT STIT présent
                        missing_stit_mask = (df_final['STIT'].isna() | (df_final['STIT'] == '')) & (df_final['TT STIT'].notna() & (df_final['TT STIT'] != ''))
                        missing_stit_count = missing_stit_mask.sum()

                        if missing_stit_count > 0:
                            logging.info(f"Il reste {missing_stit_count} lignes avec STIT manquant mais TT STIT présent")

                            # Essayer de compléter les STIT manquants en utilisant TT STIT et df_tt_stit_filtered
                            # Créer un dictionnaire pour une recherche plus rapide
                            tt_stit_to_stit = {}
                            for _, row in df_tt_stit_filtered.iterrows():
                                if pd.notna(row['TT_STIT_value']) and pd.notna(row['Sous_traitant_intervention']):
                                    tt_stit_to_stit[str(row['TT_STIT_value']).strip()] = str(row['Sous_traitant_intervention']).strip()

                            # Appliquer le dictionnaire pour compléter les valeurs manquantes
                            stit_filled = 0
                            for idx, row in df_final[missing_stit_mask].iterrows():
                                tt_stit_value = str(row['TT STIT']).strip()
                                if tt_stit_value in tt_stit_to_stit:
                                    df_final.at[idx, 'STIT'] = tt_stit_to_stit[tt_stit_value]
                                    stit_filled += 1

                            if stit_filled > 0:
                                logging.info(f"STIT complété pour {stit_filled} lignes supplémentaires")
                                stit_completed_count += stit_filled

                    # Compter les DEPT vides après toutes les complétions
                    dept_empty_final = (df_final['DEPT'].isna() | (df_final['DEPT'] == '') | (df_final['DEPT'] == 'nan')).sum()
                    logging.info(f"Après toutes les complétions: {dept_empty_final} DEPT vides sur {len(df_final)} lignes")

                    # Afficher quelques exemples de lignes avec DEPT vide pour débogage
                    if dept_empty_final > 0:
                        empty_dept_sample = df_final[df_final['DEPT'].isna() | (df_final['DEPT'] == '') | (df_final['DEPT'] == 'nan')].head(5)
                        logging.info(f"Exemples de lignes avec DEPT vide:")
                        for _, row in empty_dept_sample.iterrows():
                            logging.info(f"Ref_MUT: {row.get('Ref_MUT', 'N/A')}, PM: {row.get('PM', 'N/A')}, DEPT: {row.get('DEPT', 'N/A')}")

                    logging.info(f"Résumé de la complétion STIT/TT STIT:")
                    logging.info(f"- TT STIT complétés: {tt_stit_completed_count}")
                    logging.info(f"- STIT complétés: {stit_completed_count}")

                    # Vérifier combien de lignes ont encore des valeurs manquantes
                    if 'TT STIT' in df_final.columns:
                        still_missing_tt_stit = (df_final['TT STIT'].isna() | (df_final['TT STIT'] == '')).sum()
                        logging.info(f"Il reste {still_missing_tt_stit} lignes avec TT STIT manquant")

                    if 'STIT' in df_final.columns:
                        still_missing_stit = (df_final['STIT'].isna() | (df_final['STIT'] == '')).sum()
                        logging.info(f"Il reste {still_missing_stit} lignes avec STIT manquant")

                        # Extraire les tickets avec STIT manquant dans un fichier séparé
                        if still_missing_stit > 0:
                            logging.info(f"Extraction des {still_missing_stit} tickets avec STIT manquant dans un fichier séparé")
                            stit_missing_mask = (df_final['STIT'].isna() | (df_final['STIT'] == ''))
                            df_stit_missing = df_final[stit_missing_mask].copy()

                            # Sauvegarder les tickets avec STIT manquant dans un fichier séparé
                            stit_missing_path = output_dir / "STIT_manquant_a_verif.xlsx"

                            # Ajouter le mois au nom du fichier si un mois est sélectionné
                            if selected_month:
                                stit_missing_path = output_dir / f"STIT_manquant_a_verif_{selected_month}.xlsx"

                            # Exporter les tickets avec STIT manquant
                            with pd.ExcelWriter(stit_missing_path, engine='openpyxl') as writer:
                                # Convertir les colonnes de date en string si elles sont encore en datetime
                                for col in ['Date ouverture du cas', 'Date résolution']:
                                    if col in df_stit_missing.columns and pd.api.types.is_datetime64_any_dtype(df_stit_missing[col]):
                                        df_stit_missing[col] = df_stit_missing[col].dt.strftime('%d/%m/%Y %H:%M:%S')

                                # Créer la feuille
                                writer.book.create_sheet('STIT_manquant')
                                worksheet = writer.book['STIT_manquant']

                                # Écrire les en-têtes
                                headers = list(df_stit_missing.columns)
                                worksheet.append(headers)

                                # Écrire les données ligne par ligne
                                for _, row in df_stit_missing.iterrows():
                                    row_values = []
                                    for col in headers:
                                        # Convertir les NaN et 'nan' en None pour qu'ils apparaissent comme des cellules vides
                                        value = row[col]
                                        if pd.isna(value) or value == 'nan' or value == '':
                                            row_values.append(None)  # None sera interprété comme une cellule vide par openpyxl
                                        else:
                                            row_values.append(value)
                                    worksheet.append(row_values)

                                # Ajuster la largeur des colonnes
                                for i, column in enumerate(worksheet.columns):
                                    max_length = 0
                                    column_letter = openpyxl.utils.get_column_letter(i + 1)
                                    for cell in column:
                                        if cell.value:
                                            max_length = max(max_length, len(str(cell.value)))
                                    adjusted_width = (max_length + 2) * 1.2
                                    worksheet.column_dimensions[column_letter].width = min(adjusted_width, 50)  # Limiter à 50 pour éviter des colonnes trop larges

                            logging.info(f"Fichier {stit_missing_path} créé avec {len(df_stit_missing)} tickets avec STIT manquant")

                            # Supprimer les tickets avec STIT manquant du dataframe principal
                            df_final = df_final[~stit_missing_mask].copy()
                            logging.info(f"Les tickets avec STIT manquant ont été supprimés du dataframe principal")
                            logging.info(f"Nombre de lignes restantes dans le dataframe principal: {len(df_final)}")

                except Exception as e:
                    logging.error(f"Erreur lors de la complétion des STIT/TT STIT: {str(e)}")
                    logging.debug(traceback.format_exc())
            else:
                missing_cols = [col for col in required_columns if col not in df_manquant_ref_mut.columns]
                logging.warning(f"Colonnes manquantes dans Base_manquant_REF_MUT.xlsx: {missing_cols}")
        except Exception as e:
            logging.error(f"Erreur lors de la complétion des données manquantes: {str(e)}")
            logging.debug(traceback.format_exc())

        # Vérifier les colonnes de date avant l'export
        for col in ['Date ouverture du cas', 'Date résolution']:
            if col in df_final.columns:
                # Afficher des exemples de valeurs
                logging.info(f"Exemples de valeurs dans {col} avant export: {df_final[col].head(5).tolist()}")
                # Compter les valeurs vides
                empty_count = (df_final[col].isna() | (df_final[col] == '')).sum()
                logging.info(f"Colonne {col}: {empty_count} valeurs vides sur {len(df_final)} lignes")

        # Créer le nom du fichier en incluant le mois et l'année de résolution si un mois est sélectionné
        if selected_month:
            # Le format du mois est généralement YYYY_MM
            file_name = f"FTTH_Reporting_Final_{selected_month}.xlsx"
            logging.info(f"Nom du fichier avec mois de résolution: {file_name}")
        else:
            file_name = "FTTH_Reporting_Final.xlsx"

        result_path = output_dir / file_name
        with pd.ExcelWriter(result_path, engine='openpyxl') as writer:
            # Remplacer tous les NaN par des chaînes vides pour éviter d'avoir "nan" dans le fichier final
            for col in df_final.columns:
                if df_final[col].dtype == 'object' or pd.api.types.is_string_dtype(df_final[col]):
                    df_final[col] = df_final[col].fillna('')

            # Traitement spécial pour les colonnes de date
            for col in ['Date ouverture du cas', 'Date résolution']:
                if col in df_final.columns:
                    # Vérifier si la colonne est de type datetime
                    if pd.api.types.is_datetime64_any_dtype(df_final[col]):
                        # Compter les valeurs NaT avant conversion
                        nat_count = df_final[col].isna().sum()
                        logging.info(f"Avant conversion en string, {col} contient {nat_count} valeurs NaT sur {len(df_final)} lignes")

                        # Convertir les dates valides au format JJ/MM/AAAA HH:MM:SS
                        df_final[col] = df_final[col].dt.strftime('%d/%m/%Y %H:%M:%S')

                        # Remplacer les NaN (qui étaient des NaT) par une chaîne vide
                        df_final[col] = df_final[col].fillna('')
                    else:
                        # Si la colonne n'est pas de type datetime, s'assurer qu'elle ne contient pas de valeurs nulles
                        df_final[col] = df_final[col].fillna('')

                    # Vérifier les valeurs vides après traitement
                    empty_count = (df_final[col] == '').sum()
                    logging.info(f"Après traitement, {col} contient {empty_count} valeurs vides sur {len(df_final)} lignes")

            # Traitement spécial pour Délai_résolution - remplacer les points par des virgules
            if 'Délai_résolution' in df_final.columns:
                # Convertir en string avec virgule au lieu de point
                df_final['Délai_résolution'] = df_final['Délai_résolution'].astype(str).str.replace('.', ',', regex=False)
                logging.info("Délai_résolution formaté avec virgules pour le fichier principal")

            # Vérification finale des incohérences entre Code OI InterOP et OI
            if 'Code OI InterOP' in df_final.columns and 'OI' in df_final.columns:
                # Cas spécifique : Code OI InterOP = SFRA doit avoir OI = SFRA
                sfra_mask = (df_final['Code OI InterOP'] == 'SFRA') & (df_final['OI'] != 'SFRA')
                sfra_count = sfra_mask.sum()

                if sfra_count > 0:
                    # Afficher les valeurs incohérentes pour débogage
                    logging.warning(f"Vérification finale: Trouvé {sfra_count} lignes avec Code OI InterOP = SFRA mais OI != SFRA")

                    # Corriger les valeurs
                    df_final.loc[sfra_mask, 'OI'] = 'SFRA'
                    logging.info(f"Vérification finale: Corrigé {sfra_count} lignes pour que OI = SFRA")

            # Vérification finale pour s'assurer qu'il ne reste que des tickets avec le statut "Clos"
            if 'Statut' in df_final.columns:
                # Nettoyer la colonne Statut (supprimer les espaces, normaliser la casse)
                df_final['Statut'] = df_final['Statut'].astype(str).str.strip().str.title()

                # Vérifier s'il reste des tickets avec un statut autre que "Clos"
                non_clos_mask = ~df_final['Statut'].str.contains('Clos', case=False, na=False)
                non_clos_count = non_clos_mask.sum()

                if non_clos_count > 0:
                    # Afficher les statuts non-Clos restants
                    non_clos_statuses = df_final.loc[non_clos_mask, 'Statut'].unique()
                    logging.warning(f"Vérification finale: Trouvé {non_clos_count} lignes avec un statut autre que 'Clos': {non_clos_statuses}")

                    # Supprimer ces lignes
                    df_final = df_final[~non_clos_mask].copy()
                    logging.info(f"Vérification finale: Supprimé {non_clos_count} lignes avec un statut autre que 'Clos'")
                    logging.info(f"Nombre final de lignes: {len(df_final)}")

            # Traitement spécial pour DEPT - s'assurer qu'il est en format texte sans décimales et sans 'nan'
            if 'DEPT' in df_final.columns:
                # Vérifier les valeurs NaN avant le formatage
                nan_count = df_final['DEPT'].isna().sum()
                if nan_count > 0:
                    logging.warning(f"Trouvé {nan_count} valeurs NaN dans la colonne DEPT avant l'export")

                # Remplacer les NaN par une chaîne vide
                df_final['DEPT'] = df_final['DEPT'].fillna('')

                # Convertir en string et supprimer les décimales (.0)
                df_final['DEPT'] = df_final['DEPT'].astype(str).replace(r'\.0$', '', regex=True)

                # Remplacer 'nan' par une chaîne vide
                df_final['DEPT'] = df_final['DEPT'].replace('nan', '')

                # Vérification finale et tentative de correction des DEPT vides
                dept_empty_count = (df_final['DEPT'] == '').sum()
                if dept_empty_count > 0:
                    logging.warning(f"Il reste {dept_empty_count} DEPT vides avant l'export final")

                    # Essayer de compléter les DEPT vides en utilisant le PM
                    pm_to_dept = {}
                    for idx, row in df_final.iterrows():
                        if row['PM'] and row['DEPT'] and row['DEPT'] != '':
                            pm_to_dept[row['PM']] = row['DEPT']

                    # Appliquer le dictionnaire pour compléter les valeurs manquantes
                    dept_filled = 0
                    for idx, row in df_final[df_final['DEPT'] == ''].iterrows():
                        if row['PM'] and row['PM'] in pm_to_dept:
                            df_final.at[idx, 'DEPT'] = pm_to_dept[row['PM']]
                            dept_filled += 1

                    if dept_filled > 0:
                        logging.info(f"DEPT complété pour {dept_filled} lignes supplémentaires en utilisant les correspondances PM-DEPT")

                logging.info("DEPT formaté sans décimales et sans 'nan' pour le fichier principal")
                logging.info(f"Exemples de valeurs DEPT avant export: {df_final['DEPT'].head(10).tolist()}")

            # Exporter vers Excel avec traitement spécial pour les cellules vides
            # Créer la feuille
            writer.book.create_sheet('Rapport')
            worksheet = writer.book['Rapport']

            # Écrire les en-têtes
            headers = list(df_final.columns)
            worksheet.append(headers)

            # Écrire les données ligne par ligne
            for _, row in df_final.iterrows():
                row_values = []
                for col in headers:
                    # Convertir les NaN et 'nan' en None pour qu'ils apparaissent comme des cellules vides
                    value = row[col]
                    if pd.isna(value) or value == 'nan' or value == '':
                        row_values.append(None)  # None sera interprété comme une cellule vide par openpyxl
                    else:
                        row_values.append(value)
                worksheet.append(row_values)

            # Identifier les colonnes de date par leur nom
            date_columns = []
            for i, cell in enumerate(worksheet[1]):
                if cell.value in ['Date ouverture du cas', 'Date résolution']:
                    date_columns.append(i+1)  # +1 car les colonnes Excel commencent à 1

            # Identifier les colonnes spéciales pour le formatage
            delai_column = None
            dept_column = None
            for i, cell in enumerate(worksheet[1]):
                if cell.value == 'Délai_résolution':
                    delai_column = i+1  # +1 car les colonnes Excel commencent à 1
                elif cell.value == 'DEPT':
                    dept_column = i+1

            # Appliquer le formatage approprié à chaque cellule
            for row in worksheet.iter_rows(min_row=2):  # Commencer à la ligne 2 (après les en-têtes)
                for i, cell in enumerate(row, 1):  # i commence à 1 pour correspondre aux indices de colonnes Excel
                    if i in date_columns:
                        # Format de date français pour les colonnes de date
                        if cell.value and str(cell.value).strip():
                            cell.number_format = 'dd/mm/yyyy hh:mm:ss'
                    elif i == delai_column:
                        # Format décimal pour la colonne Délai_résolution avec virgule
                        if cell.value is not None:
                            # Appliquer un format texte pour conserver la virgule
                            cell.number_format = '@'
                    elif i == dept_column:
                        # Format texte pour la colonne DEPT
                        cell.number_format = '@'
                    else:
                        # Format texte pour les autres colonnes
                        cell.number_format = '@'

            # Ajuster la largeur des colonnes
            for i, column in enumerate(worksheet.columns):
                max_length = 0
                column_letter = openpyxl.utils.get_column_letter(i + 1)
                for cell in column:
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                adjusted_width = (max_length + 2) * 1.2
                worksheet.column_dimensions[column_letter].width = min(adjusted_width, 50)  # Limiter à 50 pour éviter des colonnes trop larges

            worksheet.sheet_view.showGridLines = False

        create_summary_sheet(result_path, df_final)

        # Déplacer le fichier dans le dossier du mois de résolution si un mois est sélectionné
        if selected_month:
            # Créer le chemin du dossier du mois
            month_dir = output_dir / str(selected_month)
            month_dir.mkdir(parents=True, exist_ok=True)

            # Chemin de destination pour le fichier (utilise le même nom que result_path)
            dest_path = month_dir / result_path.name

            try:
                # Vérifier si le fichier existe déjà à destination et le supprimer si nécessaire
                if dest_path.exists():
                    dest_path.unlink()

                # Copier le fichier vers le dossier du mois
                shutil.copy2(result_path, dest_path)
                logging.info(f"Fichier {result_path.name} copié dans le dossier du mois {selected_month}")

                # Utiliser le nouveau chemin pour la découpe
                split_final_file(dest_path, output_dir, selected_month)
            except Exception as e:
                logging.error(f"Erreur lors du déplacement du fichier vers le dossier du mois: {str(e)}")
                # En cas d'erreur, continuer avec le fichier original
                split_final_file(result_path, output_dir, selected_month)
        else:
            # Si aucun mois n'est sélectionné, procéder normalement
            split_final_file(result_path, output_dir, selected_month)

        logging.info("Traitement complet réussi !")
        return 0

    except Exception as e:
        logging.error(f"ERREUR GLOBALE : {str(e)}")
        logging.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    mp.set_start_method('spawn', force=True)
    main()