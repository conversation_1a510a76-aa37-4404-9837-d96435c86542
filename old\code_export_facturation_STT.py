import re
import traceback
import logging
import pandas as pd
from pathlib import Path
from copy import copy
from openpyxl.utils.dataframe import dataframe_to_rows
import datetime
import shutil
import os
import sys
import warnings
import multiprocessing as mp
import numpy as np
import openpyxl
from openpyxl.styles import Font, Alignment
import argparse # for command line arguments

warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')

def sanitize_filename(name):
    """Nettoie les noms de fichiers des caractères spéciaux"""
    return re.sub(r'[\\/*?:"<>|]', '_', str(name))

def setup_logging(log_file):
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

def ensure_directory_exists(directory):
    Path(directory).mkdir(parents=True, exist_ok=True)

def is_file_today(file_path):
    """Vérifie si le fichier existe et a été modifié aujourd'hui."""
    return os.path.exists(file_path) and datetime.date.fromtimestamp(os.path.getmtime(file_path)).date() == datetime.date.today()

def copy_file(source, destination):
    try:
        # Vérifier si le fichier source et le fichier destination sont identiques
        if os.path.exists(destination) and abs(os.path.getmtime(source) - os.path.getmtime(destination)) < 1:  # Ajout comparaison date de modif
             logging.info(f"Fichier déjà à jour : {os.path.basename(source)}")  # Si les fichiers sont à la même date, on ne recopie pas
             return True
        shutil.copy2(source, destination)
        logging.info(f"Fichier copié : {os.path.basename(source)}")
        return True
    except Exception as e:
        logging.error(f"Erreur copie {source} : {str(e)}")
        return False

def sanitize_excel_output(df):
    for col in df.columns:
        try:
            df[col] = df[col].astype(str).str.replace("=", "'=", regex=False)
            df[col] = df[col].str.strip()
        except (AttributeError, TypeError):
            logging.warning(f"Impossible de nettoyer la colonne '{col}'. Le type de données n'est pas compatible.")
            continue
    return df

def merge_datasets(extract_path, mut_path, sadira_path, oi_path):
    try:
        logging.info("Chargement des données...")
        df_extract = pd.read_excel(extract_path, engine='openpyxl')
        df_mut = pd.read_excel(mut_path, engine='openpyxl')
        df_sadira = pd.read_excel(sadira_path, engine='openpyxl')
        df_oi = pd.read_excel(oi_path, engine='openpyxl')

        for df in [df_extract, df_mut, df_sadira, df_oi]:
            df.columns = df.columns.astype(str).str.strip()

        if 'A pour cas parent' not in df_mut.columns or 'TT STIT' not in df_extract.columns:
            raise ValueError("Colonnes manquantes !")

        merged_df = pd.merge(
            df_mut,
            df_extract[['TT STIT', 'Elément impacté', 'Sous-traitant intervention', 'Region']],
            left_on='A pour cas parent',
            right_on='TT STIT',
            how='left'
        )

        merged_df.rename(columns={
            'Elément impacté': 'PM',
            'Sous-traitant intervention': 'STIT'
        }, inplace=True)

        merged_df.insert(19, 'Région', merged_df.pop('Region'))

        df_sadira.rename(columns={
            'Référence - LT.DET': 'PM',
            'Code postal - LT.ADR': 'DEPT',
            'Propriétaire - LT.DET': 'Propriétaire',
            'Type de zone (ZTD/ZMD) – LT.DET': 'Zone'
        }, inplace=True)
        df_sadira['DEPT'] = df_sadira['DEPT'].astype(str).str[:2]
        merged_df = pd.merge(merged_df, df_sadira[['PM', 'DEPT', 'Propriétaire', 'Zone']], on='PM', how='left')

        df_oi.rename(columns={
            'Propriétaire - LTDET': 'Propriétaire',
            'OI': 'OI'
        }, inplace=True)
        merged_df = pd.merge(merged_df, df_oi[['Propriétaire', 'Code OI InterOP', 'OI']], on='Propriétaire', how='left')

        merged_df.drop(columns=['A pour cas parent'], inplace=True)

        if 'OC' not in merged_df.columns:
            logging.warning("La colonne 'OC' n'a pas été trouvée dans le reporting ; création de 'OC' à partir de 'Code OI InterOP'.")
            merged_df['OC'] = merged_df['Code OI InterOP']

        merged_df.dropna(how='all', inplace=True)
        merged_df.fillna('', inplace=True)

        logging.info(f"Colonnes finales : {merged_df.columns.tolist()}")
        return sanitize_excel_output(merged_df)

    except Exception as e:
        logging.error(f"ERREUR fusion : {str(e)}")
        logging.debug(traceback.format_exc())
        raise

def determine_facturation_eligibility(merged_df, selected_month=None):
    """Détermine l'éligibilité à la facturation selon la typologie des cas.
       Un filtre sur le mois est appliqué avant le reste du traitement."""
    logging.info("Détermination de l'éligibilité à la facturation...")

    # Filtrer par mois AVANT le reste du traitement
    if selected_month:
        try:
            merged_df['Mois_Résolution'] = merged_df['Mois_Résolution'].astype(str)
            merged_df = merged_df[merged_df['Mois_Résolution'] == str(selected_month)].copy()  # <--- Utilisation de .copy()
            logging.info(f"Filtrage sur le mois : {selected_month}")
        except (KeyError, TypeError):
            logging.warning(f"Colonne 'Mois_Résolution' non trouvée ou type incompatible.  Pas de filtre appliqué.")
            pass  # Ne pas filtrer si la colonne n'existe pas ou si le type est mauvais
        except Exception as e:
            logging.warning(f"Erreur lors du filtrage par mois : {e}")
            pass  # Ne pas filtrer si une autre erreur se produit

    # Convertir les colonnes de date à DateTime
    for col in ['Date ouverture du cas', 'Date résolution']:
        try:
            merged_df[col] = pd.to_datetime(merged_df[col], errors='coerce')
        except KeyError:
            logging.warning(f"Colonne {col} non trouvée, vérifiez la source.")
            return merged_df

    merged_df['Délai_résolution'] = (merged_df['Date résolution'] - merged_df['Date ouverture du cas']).dt.days

    merged_df['Catégorie'] = ''
    merged_df['Facturation éligible'] = 'NON'

    conditions = [
        (merged_df['Type'] == 'SIGNALISATION') & (merged_df['Diagnostic_OC'].str.startswith('RET', na=False)),
        (merged_df['Type'] == 'SIGNALISATION') & (merged_df['Diagnostic_OC'].str.startswith('STT', na=False)),
        (merged_df['Type'] == 'EXPERTISE') & (merged_df['Diagnostic_OC'].str.startswith('RET', na=False)),
        (merged_df['Type'] == 'EXPERTISE') & (merged_df['Diagnostic_OC'].str.startswith('STT', na=False))
    ]
    categories = ['SIGRET', 'SIGSTT', 'EXPRET', 'EXPSTT']
    merged_df['Catégorie'] = np.select(conditions, categories, default='')

    # Utilisation de .loc pour éviter SettingWithCopyWarning
    merged_df.loc[(merged_df['Catégorie'] == 'SIGRET') & (merged_df['Délai_résolution'] > 14), 'Facturation éligible'] = 'NON'
    merged_df.loc[merged_df['Catégorie'] == 'SIGSTT', 'Facturation éligible'] = 'OUI'

    # Correction pour la catégorie SIGSTT
    merged_df.loc[
        (merged_df['Catégorie'] == 'SIGSTT') &
        (merged_df['Diagnostic_OC'].str.startswith('STT', na=False)) &  # Vérifie que c'est bien STT
        (merged_df['Ref_MUT'].notna()) &
        (merged_df.apply(lambda row: merged_df['Diagnostic_OC'][(merged_df['TT STIT'] == row['Ref_MUT'])].str.startswith('RET', na=False).any(), axis=1)),
        'Facturation éligible'
    ] = 'NON'  # Expertise RET existe, donc non éligible

    merged_df.loc[(merged_df['Catégorie'] == 'EXPRET') & (merged_df['Ref_MUT'].isna() | (merged_df['Ref_MUT'] == '')), 'Facturation éligible'] = 'NON'
    merged_df.loc[(merged_df['Catégorie'] == 'EXPSTT') & (merged_df['Ref_MUT'].isna() | (merged_df['Ref_MUT'] == '')), 'Facturation éligible'] = 'OUI'
    merged_df.loc[(merged_df['Catégorie'] == 'EXPSTT') & (~merged_df['Ref_MUT'].isna()) & (merged_df['Ref_MUT'] != ''), 'Facturation éligible'] = 'OUI'

    logging.info(f"Répartition des catégories: {merged_df['Catégorie'].value_counts().to_dict()}")
    logging.info(f"Nombre de cas éligibles: {(merged_df['Facturation éligible'] == 'OUI').sum()}")

    return merged_df

def create_summary_sheet_for_group(df, ws, oc, selected_month, start_row=1):
    """Crée la feuille de synthèse pour un groupe OI/OC.
       Prend en compte le mois sélectionné."""
    # Calculer les statistiques pour le groupe
    stats = {
        "Total des cas": len(df),
        "Cas éligibles": (df['Facturation éligible'] == 'OUI').sum(),
        "Pourcentage éligibilité": f"{((df['Facturation éligible'] == 'OUI').sum() / len(df) * 100):.2f}%" if len(df) > 0 else "0.00%",
        "Répartition par catégorie": df['Catégorie'].value_counts().to_dict()
    }

    # Ecriture du titre en A1
    title = f"Synthèse Facturation {oc} - Mois de résolution: {selected_month or 'Tous'}"  # Titre principal
    ws.cell(row=1, column=1, value=title)
    ws.cell(row=1, column=1).font = Font(bold=True, size=14)  # Gras et plus grand
    ws.cell(row=1, column=1).alignment = Alignment(horizontal='center')  # Centré
    ws.column_dimensions['A'].width = 90
    
   # Écrire les données dans la feuille de synthèse en A2
    for key, value in stats.items():
        ws.cell(row=start_row + 1, column=1, value=key)
        if isinstance(value, dict):
            # Écrire la répartition par catégorie
            col = 2
            for cat, count in value.items():
                ws.cell(row=start_row + 1, column=col, value=f"{cat}: {count}")
                col += 1
            start_row += 1  # On passe à la ligne suivante
        else:
            ws.cell(row=start_row + 1, column=2, value=str(value))
        start_row += 1 # Pour éviter l'écrasement on passe à la ligne suivante
    return start_row

def create_summary_sheet(result_path, df_final):
    """Crée une feuille de synthèse globale dans le fichier final, incluant des synthèses par OC_OI."""
    try:
        wb = openpyxl.load_workbook(result_path)
        # Création de la feuille de synthèse globale
        ws_synth = wb.create_sheet(title='Synthèse Globale')

        # Calcul des statistiques globales
        stats = {
            "Total des cas": len(df_final),
            "Cas éligibles": (df_final['Facturation éligible'] == 'OUI').sum(),
            "Pourcentage éligibilité": f"{((df_final['Facturation éligible'] == 'OUI').sum() / len(df_final) * 100):.2f}%" if len(df_final) > 0 else "0.00%",
            "Répartition par catégorie": df_final['Catégorie'].value_counts().to_dict()
        }
        # Écrire les données de synthèse
        row_start = 1

        # Ecriture du titre en A1
        title = f"Synthèse Facturation Globale"  # Titre principal
        ws_synth.cell(row=1, column=1, value=title)
        ws_synth.cell(row=1, column=1).font = Font(bold=True, size=14)  # Gras et plus grand
        ws_synth.cell(row=1, column=1).alignment = Alignment(horizontal='center')  # Centré
        ws_synth.column_dimensions['A'].width = 90
        
        for key, value in stats.items():
            ws_synth.cell(row=row_start + 1, column=1, value=key)
            if isinstance(value, dict):
                # Écrire la répartition par catégorie
                col = 2
                for cat, count in value.items():
                    ws_synth.cell(row=row_start + 1, column=col, value=f"{cat}: {count}")
                    col += 1
                row_start += 1  # On passe à la ligne suivante

            else:
                ws_synth.cell(row=row_start + 1, column=2, value=str(value))
            row_start += 1
        
        # Ajouter un espacement
        row_start += 1
        
        # Ajouter les synthèses par OC_OI
        grouped = df_final.groupby(['OI', 'OC'])
        
        # Titre pour la section des synthèses par OC_OI
        section_title = "Synthèse par OC et OI"
        ws_synth.cell(row=row_start, column=1, value=section_title)
        ws_synth.cell(row=row_start, column=1).font = Font(bold=True, size=12)
        row_start += 2  # Espacement après le sous-titre
        
        # En-têtes des colonnes pour le tableau des synthèses OC_OI
        headers = ["OI", "OC", "Total des cas", "Cas éligibles", "Pourcentage éligibilité"]
        for col_idx, header in enumerate(headers, 1):
            ws_synth.cell(row=row_start, column=col_idx, value=header)
            ws_synth.cell(row=row_start, column=col_idx).font = Font(bold=True)
        row_start += 1
        
        # Pour chaque groupe OI/OC, ajouter une ligne de statistiques
        for (oi, oc), group in grouped:
            col = 1
            # OI
            ws_synth.cell(row=row_start, column=col, value=oi)
            col += 1
            
            # OC
            ws_synth.cell(row=row_start, column=col, value=oc)
            col += 1
            
            # Total des cas
            total_cases = len(group)
            ws_synth.cell(row=row_start, column=col, value=total_cases)
            col += 1
            
            # Cas éligibles
            eligible_cases = (group['Facturation éligible'] == 'OUI').sum()
            ws_synth.cell(row=row_start, column=col, value=eligible_cases)
            col += 1
            
            # Pourcentage éligibilité
            eligibility_percentage = f"{(eligible_cases / total_cases * 100):.2f}%" if total_cases > 0 else "0.00%"
            ws_synth.cell(row=row_start, column=col, value=eligibility_percentage)
            
            row_start += 1
        
        # Ajouter un espacement
        row_start += 2
        
        # Ajouter un tableau de répartition des catégories par OC_OI
        section_title = "Répartition des catégories par OC et OI"
        ws_synth.cell(row=row_start, column=1, value=section_title)
        ws_synth.cell(row=row_start, column=1).font = Font(bold=True, size=12)
        row_start += 2  # Espacement après le sous-titre
        
        # Récupérer toutes les catégories uniques
        all_categories = sorted(df_final['Catégorie'].unique())
        
        # En-têtes des colonnes pour le tableau des catégories
        cat_headers = ["OI", "OC"] + list(all_categories)
        for col_idx, header in enumerate(cat_headers, 1):
            ws_synth.cell(row=row_start, column=col_idx, value=header)
            ws_synth.cell(row=row_start, column=col_idx).font = Font(bold=True)
        row_start += 1
        
        # Pour chaque groupe OI/OC, ajouter une ligne avec la répartition des catégories
        for (oi, oc), group in grouped:
            col = 1
            # OI
            ws_synth.cell(row=row_start, column=col, value=oi)
            col += 1
            
            # OC
            ws_synth.cell(row=row_start, column=col, value=oc)
            col += 1
            
            # Comptage par catégorie
            cat_counts = group['Catégorie'].value_counts().to_dict()
            
            # Remplir les valeurs pour chaque catégorie
            for cat in all_categories:
                count = cat_counts.get(cat, 0)
                ws_synth.cell(row=row_start, column=col, value=count)
                col += 1
            
            row_start += 1
            
        # Formatage:
        for row in ws_synth.iter_rows():
            for cell in row:
                cell.number_format = '@'
        ws_synth.sheet_view.showGridLines = False

        # Réglage de la largeur des colonnes
        ws_synth.column_dimensions['A'].width = 20  # OI
        ws_synth.column_dimensions['B'].width = 20  # OC
        for col in "CDEFGHIJKLM":  # Autres colonnes (ajuster selon le nombre de colonnes)
            if col in ws_synth.column_dimensions:
                ws_synth.column_dimensions[col].width = 20

        wb.save(result_path)
        logging.info("Feuille de synthèse globale créée avec succès, incluant les synthèses par OC_OI.")

    except Exception as e:
        logging.error(f"ERREUR création synthèse globale: {str(e)}")
        logging.debug(traceback.format_exc())

def split_final_file(result_path, output_dir, selected_month=None):
    """Découpe le fichier final par OI et OC et inclut la feuille de synthèse correspondante."""
    try:
        df = pd.read_excel(result_path, engine='openpyxl')
        wb_main = openpyxl.load_workbook(result_path)

        if selected_month:
            output_dir = output_dir / str(selected_month)
        output_dir.mkdir(parents=True, exist_ok=True)

        xpf_dir = output_dir / 'XPF'
        sfra_dir = output_dir / 'SFRA'
        xpf_dir.mkdir(parents=True, exist_ok=True)
        sfra_dir.mkdir(parents=True, exist_ok=True)

        grouped = df.groupby(['OI', 'OC'])

        for (oi, oc), group in grouped:
            target_dir = xpf_dir if oi == 'XPF' else sfra_dir if oi == 'SFRA' else None
            if not target_dir:
                continue

            group_clean = sanitize_excel_output(group)
            base_name = result_path.stem
            safe_oc = sanitize_filename(oc)
            safe_oi = sanitize_filename(oi)
            new_name = f"{base_name}_{safe_oc}_{safe_oi}_.xlsx"
            new_path = target_dir / new_name

            # Création d'un nouveau workbook
            wb_new = openpyxl.Workbook()
            ws_new = wb_new.active
            ws_new.title = 'Data'

            # Écriture des données
            for r in dataframe_to_rows(group_clean, index=False, header=True):
                ws_new.append(r)
            # Formatage :
            for row in ws_new.iter_rows():
                for cell in row:
                    cell.number_format = '@'
            ws_new.sheet_view.showGridLines = False

            # Création de la feuille de synthèse par OI/OC
            sheet_name = 'Synthèse Facturation'
            ws_synth = wb_new.create_sheet(title=sheet_name)  # Créer la feuille dans chaque fichier

            # Calcul et écriture des statistiques pour ce groupe
            row_start = 1
            row_start = create_summary_sheet_for_group(group, ws_synth, oc, selected_month, row_start)  # On passe OC et selected_month

            # Réglage de la largeur des colonnes
            for col in "A":  # Colonnes à ajuster
                ws_synth.column_dimensions[col].width = 80
                
            for col in "BCDE":  # Colonnes à ajuster
                ws_synth.column_dimensions[col].width = 30  # Ajuste la largeur
            
            
            # Formatage de la feuille de synthèse
            for row in ws_synth.iter_rows():
                for cell in row:
                    cell.number_format = '@'
            ws_synth.sheet_view.showGridLines = False

            # Sauvegarde
            wb_new.save(new_path)
            logging.info(f"Fichier créé : {new_path}")
        logging.info("Découpage terminé avec succès.")
        return True

    except Exception as e:
        logging.error(f"ERREUR découpage : {str(e)}")
        logging.debug(traceback.format_exc())
        return False

def get_selected_month(reporting_file):
    """
    Permet à l'utilisateur de sélectionner un mois parmi ceux disponibles.
    Utilise une approche simple de sélection par numéro.
    """
    try:
        df = pd.read_excel(reporting_file, engine='openpyxl')
        df['Mois_Résolution'] = df['Mois_Résolution'].astype(str)
        available_months = sorted(df['Mois_Résolution'].unique().tolist())
        
        if not available_months:
            logging.warning("Aucun mois de résolution trouvé dans le fichier de reporting.")
            return None

        # Afficher les mois disponibles avec des numéros
        print("\nMois de résolution disponibles :")
        for i, month in enumerate(available_months, 1):
            print(f"{i}. {month}")
        print("0. Tous les mois")

        while True:
            try:
                choice = input("\nEntrez le numéro du mois à sélectionner (0 pour tous) : ").strip()
                
                # Convertir l'entrée en entier
                choice_num = int(choice)
                
                # Vérifier la validité du choix
                if choice_num == 0:
                    return None  # Tous les mois
                elif 1 <= choice_num <= len(available_months):
                    return available_months[choice_num - 1]
                else:
                    print("Choix invalide. Veuillez entrer un numéro valide.")
            
            except ValueError:
                print("Veuillez entrer un nombre valide.")
    
    except Exception as e:
        logging.error(f"Erreur lors de la sélection du mois : {e}")
        return None

def main():
    base_path = Path(r"C:\Users\<USER>\Desktop\KPI")
    today_str = datetime.date.today().strftime("%Y_%m_%d")
    output_dir = base_path / "Facturation_STT" / today_str
    ensure_directory_exists(output_dir)
    log_file = output_dir / "kpi_processing.log"
    setup_logging(log_file)
    logging.info(f"Début traitement - {today_str}")
    src_files = {
        "extract": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\_FTTH_TT_STIT.xlsx"),
        "reporting": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\FTTH - Reporting cas STC_REFMUT.xlsx"),
        "sadira": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\FTTH_Liste_PM_source_sadira.xlsx"),
        "oi": Path(r"U:\Lyon\STC Rési Bron\ISABEL\FTTH\Reporting\Liste_OI.xlsx"),
        "manquant_ref_mut": Path(r"U:\Lyon\STC Rési Bron\ISABEL\FTTH\Reporting\Base_manquant_REF_MUT.xlsx")
    }

    # Modifier copy_file pour ne copier que si le fichier source est plus récent que la destination
    try:
        dest_files = {key: output_dir / src_files[key].name for key in src_files}
        # Avant de copier, on vérifie la date de modification du fichier source et de la destination
        for key in src_files:
            copy_file(src_files[key], dest_files[key])

        df_merged = merge_datasets(
            dest_files["extract"],
            dest_files["reporting"],
            dest_files["sadira"],
            dest_files["oi"]
        )

        # Demander à l'utilisateur de choisir le mois
        selected_month = get_selected_month(dest_files["reporting"])

        # Appliquer le filtre Mois_Résolution avant le calcul
        df_final = determine_facturation_eligibility(df_merged, selected_month)
        
        # Compléter les informations manquantes avec le fichier Base_manquant_REF_MUT.xlsx
        logging.info("Vérification et complétion des données manquantes avec Base_manquant_REF_MUT.xlsx...")
        try:
            # Charger le fichier de référence
            df_manquant_ref_mut = pd.read_excel(dest_files["manquant_ref_mut"], engine='openpyxl')
            df_manquant_ref_mut.columns = df_manquant_ref_mut.columns.astype(str).str.strip()
            
            # Afficher les colonnes trouvées pour le débogage
            logging.info(f"Colonnes trouvées dans Base_manquant_REF_MUT.xlsx: {df_manquant_ref_mut.columns.tolist()}")
            
            # Vérifier si les colonnes nécessaires existent - adaptation à la structure réelle du fichier
            required_columns = ['Ref_MUT', 'PM', 'DEPT', 'Code OI InterOP']
            if all(col in df_manquant_ref_mut.columns for col in required_columns):
                # Identifier les lignes avec OI manquant
                missing_oi_mask = df_final['OI'].isna() | (df_final['OI'] == '')
                missing_count = missing_oi_mask.sum()
                logging.info(f"Nombre de lignes avec OI manquant: {missing_count}")
                
                # Compteur pour les lignes complétées
                completed_count = 0
                
                # Pour chaque ligne avec OI manquant, on essaie de trouver les informations dans df_manquant_ref_mut
                for idx, row in df_final[missing_oi_mask].iterrows():
                    ref_mut_value = row['Ref_MUT'] if 'Ref_MUT' in row else None
                    if pd.notna(ref_mut_value) and ref_mut_value != '':
                        # Chercher dans df_manquant_ref_mut les lignes correspondant à la Ref_MUT
                        matching_rows = df_manquant_ref_mut[df_manquant_ref_mut['Ref_MUT'] == ref_mut_value]
                        if not matching_rows.empty:
                            # Prendre la première correspondance
                            match = matching_rows.iloc[0]
                            
                            # Compléter les informations manquantes
                            if pd.isna(df_final.at[idx, 'DEPT']) or df_final.at[idx, 'DEPT'] == '':
                                df_final.at[idx, 'DEPT'] = match['DEPT']
                                logging.info(f"DEPT complété pour Ref_MUT: {ref_mut_value}")
                            
                            if pd.isna(df_final.at[idx, 'Code OI InterOP']) or df_final.at[idx, 'Code OI InterOP'] == '':
                                df_final.at[idx, 'Code OI InterOP'] = match['Code OI InterOP']
                                logging.info(f"Code OI InterOP complété pour Ref_MUT: {ref_mut_value}")
                            
                            completed_count += 1
                
                # Maintenant, utiliser le fichier Liste_OI.xlsx pour compléter les OI manquants
                try:
                    # Recharger Liste_OI pour être sûr d'avoir les données les plus à jour
                    df_oi = pd.read_excel(dest_files["oi"], engine='openpyxl')
                    df_oi.columns = df_oi.columns.astype(str).str.strip()
                    
                    # Renommer pour correspondre à nos colonnes
                    df_oi.rename(columns={
                        'Propriétaire - LTDET': 'Propriétaire',
                        'OI': 'OI'
                    }, inplace=True)
                    
                    # Pour chaque ligne où OI est vide mais Code OI InterOP existe
                    missing_oi_with_code = (df_final['OI'].isna() | (df_final['OI'] == '')) & (df_final['Code OI InterOP'].notna() & (df_final['Code OI InterOP'] != ''))
                    oi_fill_count = 0
                    
                    for idx, row in df_final[missing_oi_with_code].iterrows():
                        code_oi = row['Code OI InterOP']
                        matching_oi_rows = df_oi[df_oi['Code OI InterOP'] == code_oi]
                        
                        if not matching_oi_rows.empty:
                            df_final.at[idx, 'OI'] = matching_oi_rows.iloc[0]['OI']
                            oi_fill_count += 1
                    
                    logging.info(f"OI complété pour {oi_fill_count} lignes à partir de Liste_OI.xlsx")
                
                except Exception as e:
                    logging.error(f"Erreur lors de la complétion des OI avec Liste_OI.xlsx: {str(e)}")
                
                logging.info(f"Informations complétées pour {completed_count} lignes sur {missing_count} manquantes")
            else:
                missing_cols = [col for col in required_columns if col not in df_manquant_ref_mut.columns]
                logging.warning(f"Colonnes manquantes dans Base_manquant_REF_MUT.xlsx: {missing_cols}")
        except Exception as e:
            logging.error(f"Erreur lors de la complétion des données manquantes: {str(e)}")
            logging.debug(traceback.format_exc())

        result_path = output_dir / "FTTH_Reporting_Final.xlsx"
        with pd.ExcelWriter(result_path, engine='openpyxl') as writer:
            df_final.to_excel(writer, index=False, sheet_name='Rapport')
            worksheet = writer.sheets['Rapport']
            for row in worksheet.iter_rows():
                for cell in row:
                    cell.number_format = '@'
            worksheet.sheet_view.showGridLines = False

        create_summary_sheet(result_path, df_final)
        split_final_file(result_path, output_dir, selected_month)
        logging.info("Traitement complet réussi !")
        return 0

    except Exception as e:
        logging.error(f"ERREUR GLOBALE : {str(e)}")
        logging.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    mp.set_start_method('spawn', force=True)
    main()