# Amélioration de la Logique d'Évolution SIGNALISATION → EXPERTISE

## 🎯 Objectif

Implémenter la logique d'évolution des tickets : quand `Ex_type` n'est pas vide, cela signifie qu'un ticket SIGNALISATION s'est transformé en EXPERTISE. Il faut créer deux lignes distinctes pour refléter cette évolution.

## 🔄 Nouvelle Logique Implémentée

### Principe
- **Détection** : Lignes avec `Ex_type` non vide indiquent une évolution
- **Transformation** : 1 ligne originale → 2 lignes (SIGNALISATION + EXPERTISE)
- **Suppression** : La ligne originale est remplacée par les 2 nouvelles

### Workflow
```
Ligne originale (Ex_type = 'SIGNALISATION')
    ↓
SUPPRESSION de la ligne originale
    ↓
CRÉATION de 2 nouvelles lignes:
    ├── SIGNALISATION (avec Ex_* → colonnes standards)
    └── EXPERTISE (avec Date_New_exp, etc.)
```

## 📋 Mapping des Champs

### SIGNALISATION (données Ex_*)
| Champ source | Champ cible | Description |
|--------------|-------------|-------------|
| `Ex_source` | `Souce_OC` | Source de la signalisation |
| `Ex_famille` | `Famille_OC` | Famille de la signalisation |
| `Ex_diagnostic` | `Diagnostic_OC` | Diagnostic de la signalisation |
| `Date_resolution_SIG` | `Date résolution` | Date de résolution signalisation |
| `Mois résol sig` | `Mois résolution` | Mois de résolution signalisation |
| `Délai_JC2_Resol sig` | `Délai résol JC` | Délai de résolution signalisation |

### EXPERTISE (nouvelles données)  
| Champ source | Champ cible | Description |
|--------------|-------------|-------------|
| `Date_New_exp` | `Date ouverture du cas` | Nouvelle date d'ouverture expertise |
| `Mois ouv new exp` | `Mois Ouv` | Nouveau mois d'ouverture expertise |
| `Semaine ouv new exp` | `semaine ouv` | Nouvelle semaine d'ouverture expertise |
| `Délai_JC3_Resol exp` | `Délai résol JC` | Délai de résolution expertise |

## 🔧 Modifications Techniques

### Fichier Modifié
`signalization_duplication_logic.py` - Refonte complète de la fonction `duplicate_signalization_tickets()`

### Changements Clés
1. **Détection** : `Ex_type` non vide au lieu de filtrer les lignes "SIGNALISATION"
2. **Création** : 2 copies de chaque ligne évoluée
3. **Mapping** : Séparé entre SIGNALISATION et EXPERTISE
4. **Suppression** : Lignes originales remplacées par les nouvelles

### Code Principal
```python
# Détection des évolutions
evolution_mask = df['Ex_type'].notna() & (df['Ex_type'] != '') & (df['Ex_type'] != 0)
evolution_rows = df[evolution_mask].copy()

# Création SIGNALISATION
signalization_rows = evolution_rows.copy()
signalization_rows['Type'] = 'SIGNALISATION'

# Création EXPERTISE  
expertise_rows = evolution_rows.copy()
expertise_rows['Type'] = 'EXPERTISE'

# Application des mappings
# ... (voir code pour détails)

# Suppression originales + ajout nouvelles
df_without_evolved = df[~evolution_mask].copy()
result_df = pd.concat([df_without_evolved, signalization_rows, expertise_rows], ignore_index=True)
```

## ✅ Tests de Validation

### Test Simple
- **Input** : 2 lignes dont 1 avec `Ex_type='SIGNALISATION'`
- **Output** : 3 lignes (1 SIGNALISATION + 2 EXPERTISE)
- **Résultat** : ✅ RÉUSSI

### Vérifications
- [x] Détection correcte des évolutions via `Ex_type`
- [x] Création de 2 lignes par évolution
- [x] Mapping SIGNALISATION correct (Ex_* → standards)
- [x] Mapping EXPERTISE correct (Date_New_exp, etc.)
- [x] Préservation des lignes sans évolution
- [x] Tri par Identifiant cas

## 📊 Exemple Concret

### Avant (1 ligne)
```
Identifiant: C12345
Type: EXPERTISE  
Date ouverture: 2024-01-01
Date résolution: 2024-01-15
Ex_type: SIGNALISATION
Ex_source: SIG_SRC
Date_resolution_SIG: 2024-01-10
Date_New_exp: 2024-01-12
```

### Après (2 lignes)
```
1. SIGNALISATION
   Identifiant: C12345
   Type: SIGNALISATION
   Date ouverture: 2024-01-01        (originale)
   Date résolution: 2024-01-10       (Date_resolution_SIG)
   Souce_OC: SIG_SRC                 (Ex_source)

2. EXPERTISE
   Identifiant: C12345
   Type: EXPERTISE  
   Date ouverture: 2024-01-12        (Date_New_exp)
   Date résolution: 2024-01-15       (originale)
   Souce_OC: CLIENT                  (originale préservée)
```

## 🎯 Impact sur la Facturation

### Comptage Double
- **SIGNALISATION** : Facturée avec sa date de résolution (Date_resolution_SIG)
- **EXPERTISE** : Facturée avec sa date de résolution (originale)

### Mois de Facturation
- **SIGNALISATION** : Basé sur `Mois résol sig`  
- **EXPERTISE** : Basé sur `Mois résolution` original

## 🔄 Intégration

La fonction s'intègre automatiquement dans le workflow principal :

```python
# Dans code_export_facturation_STT_V6.py
from signalization_duplication_logic import duplicate_signalization_tickets

# Après enrichissement des données
df_merged = duplicate_signalization_tickets(df_merged)
```

## 📈 Bénéfices

1. **Précision** : Facturation séparée des 2 phases (signalisation + expertise)
2. **Traçabilité** : Conservation de l'historique complet d'évolution
3. **Flexibilité** : Calculs de KPI distincts par type
4. **Conformité** : Respect du processus métier réel

## 🧪 Prochaines Étapes

1. Tests avec données réelles du fichier `300_FTTH_extract3.0.xlsx`
2. Validation sur un échantillon
3. Déploiement en production
4. Monitoring des résultats

---

**Status** : ✅ IMPLÉMENTÉ ET TESTÉ  
**Version** : 2.0  
**Date** : 2025-01-08