#!/usr/bin/env python3
"""
Test simple de la logique d'évolution
"""

import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from signalization_duplication_logic import duplicate_signalization_tickets

def test_simple():
    """Test simple avec 1 ligne qui évolue"""
    
    print("=== TEST SIMPLE ===")
    
    # 1 ligne qui évolue + 1 ligne normale
    test_data = {
        'Identifiant cas': ['C12345', 'C67890'],
        'Ref OC': ['REF001', 'REF002'], 
        'Type': ['EXPERTISE', 'EXPERTISE'],
        'Date ouverture du cas': ['2024-01-01', '2024-01-02'],
        'Date résolution': ['2024-01-15', '2024-01-20'],
        'Souce_OC': ['CLIENT', 'INTERNE'],
        'Famille_OC': ['RACCORDEMENT', 'MAINTENANCE'],
        'Diagnostic_OC': ['PB_TECH', 'PB_INFRA'],
        
        # Seule la première ligne évolue
        'Ex_type': ['SIGNALISATION', ''],
        'Ex_source': ['SIG_SRC', ''],
        'Ex_famille': ['SIG_FAM', ''],
        'Ex_diagnostic': ['SIG_DIAG', ''],
        'Date_resolution_SIG': ['2024-01-10', ''],
        'Date_New_exp': ['2024-01-12', '']
    }
    
    df = pd.DataFrame(test_data)
    print(f"Lignes avant: {len(df)}")
    
    result = duplicate_signalization_tickets(df)
    print(f"Lignes après: {len(result)}")
    
    # Vérifications
    sig_count = (result['Type'] == 'SIGNALISATION').sum()
    exp_count = (result['Type'] == 'EXPERTISE').sum()
    
    print(f"SIGNALISATION: {sig_count}")
    print(f"EXPERTISE: {exp_count}")
    
    if sig_count == 1 and exp_count == 2:
        print("TEST REUSSI!")
    else:
        print("TEST ECHOUE!")
        
    # Vérifier les données
    c12345_rows = result[result['Identifiant cas'] == 'C12345']
    print(f"\nC12345 - Nombre de lignes: {len(c12345_rows)}")
    
    for _, row in c12345_rows.iterrows():
        print(f"  Type: {row['Type']}, Date ouv: {row['Date ouverture du cas']}, Date res: {row['Date résolution']}")
    
    return result

if __name__ == "__main__":
    test_simple()