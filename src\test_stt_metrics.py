#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test spécifique des métriques STT pour vérifier qu'elles ne causent pas d'erreur Excel
"""

import sys
import os
from pathlib import Path
import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment

# Ajouter le répertoire parent au PATH pour importer le module principal
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_stt_metrics_with_problematic_data():
    """Test avec des données problématiques qui peuvent causer des erreurs Excel"""
    try:
        from code_export_facturation_STT_V6 import create_summary_sheet
        
        # Créer des données de test avec des valeurs problématiques
        problematic_data = {
            'Diagnostic_OC': [
                'STT001', 'STT002', None, 'STT003', '', 
                float('nan'), 'AUTRE001', 'STT004', 'STT005', None
            ],
            'Facturation éligible': [
                'OUI', 'NON', None, 'NON', '', 
                'O<PERSON>', 'OUI', None, 'NON', 'OUI'
            ],
            'Catégorie': [
                'Cat1', 'Cat1', None, 'Cat2', 'Cat1', 
                'Cat2', None, 'Cat1', 'Cat2', ''
            ],
            'OI': ['OI1', 'OI1', 'OI2', None, 'OI1', 'OI2', 'OI1', 'OI2', '', None],
            'OC': ['OC1', None, 'OC2', 'OC1', '', 'OC2', 'OC1', 'OC2', 'OC1', None]
        }
        
        df_test = pd.DataFrame(problematic_data)
        print(f"[TEST] Création d'un DataFrame avec {len(df_test)} lignes de données problématiques")
        print(f"[INFO] Valeurs nulles dans Diagnostic_OC: {df_test['Diagnostic_OC'].isna().sum()}")
        print(f"[INFO] Valeurs nulles dans Facturation éligible: {df_test['Facturation éligible'].isna().sum()}")
        
        # Créer un fichier Excel temporaire
        test_file = Path("test_stt_metrics.xlsx")
        
        # Créer un fichier Excel basique
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = 'Rapport'
        
        # Écrire les en-têtes
        for col, header in enumerate(df_test.columns, 1):
            ws.cell(row=1, column=col, value=header)
        
        # Écrire les données (y compris les valeurs nulles)
        for row_idx, (_, row) in enumerate(df_test.iterrows(), 2):
            for col_idx, value in enumerate(row, 1):
                # Traitement spécial pour les valeurs nulles/NaN
                if pd.isna(value) or value is None:
                    ws.cell(row=row_idx, column=col_idx, value="")
                else:
                    ws.cell(row=row_idx, column=col_idx, value=value)
        
        wb.save(test_file)
        
        # Tester la fonction create_summary_sheet avec ces données problématiques
        print("[TEST] Création de la feuille de synthèse avec données problématiques...")
        create_summary_sheet(test_file, df_test)
        
        # Vérifier que le fichier s'ouvre sans erreur
        print("[TEST] Vérification de l'ouverture du fichier...")
        wb_test = openpyxl.load_workbook(test_file)
        
        if 'Synthèse Globale' in wb_test.sheetnames:
            print("[OK] Feuille 'Synthèse Globale' créée avec succès")
            
            # Vérifier le contenu des métriques STT
            ws_synth = wb_test['Synthèse Globale']
            
            # Chercher les métriques STT dans la feuille
            stt_metrics_found = {}
            for row in range(1, ws_synth.max_row + 1):
                cell_value = ws_synth.cell(row=row, column=1).value
                if cell_value and 'STT' in str(cell_value):
                    metric_value = ws_synth.cell(row=row, column=2).value
                    stt_metrics_found[cell_value] = metric_value
                    print(f"[INFO] {cell_value}: {metric_value}")
            
            # Vérifier que toutes les métriques STT sont présentes
            expected_metrics = [
                "Total STT",
                "STT éligibles", 
                "STT exclus par réitération",
                "Taux exclusion réitération"
            ]
            
            all_found = True
            for metric in expected_metrics:
                if not any(metric in str(key) for key in stt_metrics_found.keys()):
                    print(f"[WARNING] Métrique manquante: {metric}")
                    all_found = False
            
            if all_found:
                print("[OK] Toutes les métriques STT sont présentes")
            
            # Tester l'ouverture du fichier par Excel (simulation)
            print("[TEST] Simulation d'ouverture par Excel...")
            
            # Vérifier qu'il n'y a pas de valeurs NaN ou problématiques dans les cellules
            problematic_cells = []
            for row in range(1, ws_synth.max_row + 1):
                for col in range(1, ws_synth.max_column + 1):
                    cell_value = ws_synth.cell(row=row, column=col).value
                    if cell_value is not None and str(cell_value) in ['nan', 'inf', '-inf']:
                        problematic_cells.append(f"Row {row}, Col {col}: {cell_value}")
            
            if problematic_cells:
                print(f"[WARNING] Cellules problématiques trouvées:")
                for cell in problematic_cells:
                    print(f"  {cell}")
                return False
            else:
                print("[OK] Aucune cellule problématique trouvée")
                return True
                
        else:
            print("[ERREUR] Feuille 'Synthèse Globale' non trouvée")
            return False
            
    except Exception as e:
        print(f"[ERREUR] Test échoué: {str(e)}")
        import traceback
        print(f"[DEBUG] {traceback.format_exc()}")
        return False
    finally:
        # Nettoyer le fichier de test
        if 'test_file' in locals() and test_file.exists():
            test_file.unlink()

def main():
    print("=== TEST METRIQUES STT AVEC DONNEES PROBLEMATIQUES ===")
    success = test_stt_metrics_with_problematic_data()
    
    if success:
        print("\n[SUCCESS] Les métriques STT sont maintenant sécurisées!")
        print("Les fichiers Excel devraient s'ouvrir sans message d'erreur.")
    else:
        print("\n[FAIL] Des problèmes persistent avec les métriques STT.")

if __name__ == "__main__":
    main()