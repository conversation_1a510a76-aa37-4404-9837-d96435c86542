#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Correctif pour les erreurs de formules Excel dans la génération FTTH
Corrige spécifiquement les problèmes de la feuille "Synthèse Globale"
"""

import openpyxl
from openpyxl.styles import Font, Alignment
import pandas as pd
import logging
from pathlib import Path

def fix_excel_file(file_path):
    """
    Corrige les erreurs de formules dans le fichier Excel existant
    """
    try:
        # Charger le fichier Excel
        wb = openpyxl.load_workbook(file_path)
        
        # Vérifier si la feuille "Synthèse Globale" existe
        if 'Synthèse Globale' in wb.sheetnames:
            print("Suppression de l'ancienne feuille 'Synthèse Globale'...")
            wb.remove(wb['Synthèse Globale'])
        
        # Charger les données de la feuille principale
        if 'Rapport' in wb.sheetnames:
            ws_data = wb['Rapport']
        else:
            # Utiliser la première feuille disponible
            ws_data = wb.active
        
        # Convertir en DataFrame pour les calculs
        data = []
        headers = []
        
        # Lire les en-têtes
        for cell in ws_data[1]:
            headers.append(cell.value)
        
        # Lire les données
        for row in ws_data.iter_rows(min_row=2, values_only=True):
            data.append(row)
        
        df = pd.DataFrame(data, columns=headers)
        
        # Créer une nouvelle feuille de synthèse corrigée
        ws_synth = wb.create_sheet(title='Synthèse Globale')
        
        # Calculs sans formules Excel - directement avec les valeurs
        total_cas = len(df)
        cas_eligibles = (df['Facturation éligible'] == 'OUI').sum() if 'Facturation éligible' in df.columns else 0
        pourcentage_eligibilite = (cas_eligibles / total_cas * 100) if total_cas > 0 else 0
        
        # STT analysis détaillée si la colonne existe
        stt_total = 0
        stt_eligible = 0
        stt_non_eligible = 0
        taux_exclusion_reiteration = 0
        
        if 'Diagnostic_OC' in df.columns:
            stt_total = df['Diagnostic_OC'].str.startswith('STT', na=False).sum()
            stt_non_eligible = ((df['Diagnostic_OC'].str.startswith('STT', na=False)) & 
                               (df['Facturation éligible'] == 'NON')).sum()
            stt_eligible = stt_total - stt_non_eligible
            
            # Calcul du taux d'exclusion par réitération
            if stt_total > 0:
                taux_exclusion_reiteration = (stt_non_eligible / stt_total * 100)
        
        # Écrire les données sans formules
        row = 1
        
        # Titre principal
        ws_synth.cell(row=row, column=1, value="Synthèse Facturation Globale")
        ws_synth.cell(row=row, column=1).font = Font(bold=True, size=14)
        ws_synth.cell(row=row, column=1).alignment = Alignment(horizontal='center')
        row += 2
        
        # Statistiques principales avec métriques STT demandées
        stats_data = [
            ("Total des cas", total_cas),
            ("Cas éligibles", cas_eligibles),
            ("Pourcentage éligibilité", f"{pourcentage_eligibilite:.2f}%"),
            ("", ""),  # Ligne vide
            ("== ANALYSE STT DETAILLEE ==", ""),
            ("Total STT", stt_total),
            ("STT éligibles", stt_eligible),
            ("STT exclus par réitération", stt_non_eligible),
            ("Taux exclusion réitération", f"{taux_exclusion_reiteration:.2f}%"),
        ]
        
        for label, value in stats_data:
            ws_synth.cell(row=row, column=1, value=label)
            if label and not label.startswith("=="):
                ws_synth.cell(row=row, column=2, value=value)
            elif label.startswith("=="):
                ws_synth.cell(row=row, column=1).font = Font(bold=True)
            row += 1
        
        row += 2
        
        # Synthèse par OC et OI si les colonnes existent
        if 'OI' in df.columns and 'OC' in df.columns:
            ws_synth.cell(row=row, column=1, value="Synthèse par OC et OI")
            ws_synth.cell(row=row, column=1).font = Font(bold=True, size=12)
            row += 1
            
            # En-têtes
            headers_oc = ["OI", "OC", "Total des cas", "Cas éligibles", "Pourcentage éligibilité"]
            for col_idx, header in enumerate(headers_oc, 1):
                ws_synth.cell(row=row, column=col_idx, value=header)
                ws_synth.cell(row=row, column=col_idx).font = Font(bold=True)
            row += 1
            
            # Grouper par OI et OC
            grouped = df.groupby(['OI', 'OC'])
            for (oi, oc), group in grouped:
                total_group = len(group)
                eligible_group = (group['Facturation éligible'] == 'OUI').sum() if 'Facturation éligible' in group.columns else 0
                percent_group = (eligible_group / total_group * 100) if total_group > 0 else 0
                
                ws_synth.cell(row=row, column=1, value=oi)
                ws_synth.cell(row=row, column=2, value=oc)
                ws_synth.cell(row=row, column=3, value=total_group)
                ws_synth.cell(row=row, column=4, value=eligible_group)
                ws_synth.cell(row=row, column=5, value=f"{percent_group:.2f}%")
                row += 1
        
        # Ajuster les largeurs de colonnes
        ws_synth.column_dimensions['A'].width = 50
        ws_synth.column_dimensions['B'].width = 20
        ws_synth.column_dimensions['C'].width = 15
        ws_synth.column_dimensions['D'].width = 15
        ws_synth.column_dimensions['E'].width = 20
        
        # Masquer les lignes de grille
        ws_synth.sheet_view.showGridLines = False
        
        # Sauvegarder le fichier corrigé
        wb.save(file_path)
        print(f"[OK] Fichier Excel corrige avec succes : {file_path}")
        
        return True
        
    except Exception as e:
        print(f"[ERREUR] Erreur lors de la correction : {str(e)}")
        logging.error(f"Erreur correction Excel : {str(e)}")
        return False

def main(target_file=None):
    """Fonction principale pour tester le correctif"""
    if target_file:
        found_file = Path(target_file)
        if not found_file.exists():
            print(f"[ERREUR] Fichier specifie introuvable : {target_file}")
            return
    else:
        # Chercher le fichier FTTH le plus récent
        search_dirs = [
            Path.cwd(),
            Path(r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_06")
        ]
        
        search_patterns = [
            "FTTH_Reporting_Final_*.xlsx",
            "*FTTH*.xlsx"
        ]
        
        found_file = None
        
        for search_dir in search_dirs:
            if not search_dir.exists():
                continue
            for pattern in search_patterns:
                files = list(search_dir.glob(pattern))
                if files:
                    # Prendre le plus récent
                    found_file = max(files, key=lambda f: f.stat().st_mtime)
                    break
            if found_file:
                break
    
    if found_file:
        print(f"Fichier trouve : {found_file}")
        success = fix_excel_file(found_file)
        if success:
            print("[SUCCESS] Correction terminee avec succes!")
        else:
            print("[FAIL] Echec de la correction")
    else:
        print("[ERREUR] Aucun fichier Excel FTTH trouve")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        main(sys.argv[1])
    else:
        main()