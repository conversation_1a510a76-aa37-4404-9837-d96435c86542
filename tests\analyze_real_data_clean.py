#!/usr/bin/env python3
"""
Analyse des vraies données pour comprendre la logique de signalisation
"""

import pandas as pd
import os

def analyze_signalization_in_real_file():
    """Analyser le fichier 300_FTTH_extract3.0.xlsx pour comprendre les données de signalisation"""
    
    # Essayer plusieurs chemins possibles
    possible_paths = [
        r"D:\donnees\FTTH\export\300_FTTH_extract3.0.xlsx",
        r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\300_FTTH_extract3.0.xlsx",
        r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\300_FTTH_extract3.0.xlsx"
    ]
    
    file_path = None
    for path in possible_paths:
        if os.path.exists(path):
            file_path = path
            print(f"FICHIER TROUVE: {path}")
            break
    
    if not file_path:
        print("FICHIER NON TROUVE dans les chemins suivants:")
        for path in possible_paths:
            print(f"  - {path}")
        return
        
    print("=== ANALYSE DES DONNEES REELLES DE SIGNALISATION ===")
    
    try:
        # Lire le fichier
        print("Chargement du fichier...")
        df = pd.read_excel(file_path)
        print(f"Fichier charge: {len(df)} lignes, {len(df.columns)} colonnes")
        
        # Analyser un échantillon
        sample_size = min(1000, len(df))
        sample = df.head(sample_size)
        print(f"Analyse d'un echantillon de {len(sample)} lignes...")
        
        # Champs de signalisation à analyser
        sig_fields = ['Date_resolution_SIG', 'Ex_type', 'Ex_source', 'Ex_famille', 'Ex_diagnostic', 
                     'Date_New_exp', 'Mois résol sig', 'Semaine résol sig', 'Délai_JC2_Resol sig', 
                     'Délai_JC3_Resol exp', 'Statut_SIG']
        
        available_sig_fields = [field for field in sig_fields if field in sample.columns]
        print(f"Champs de signalisation disponibles: {available_sig_fields}")
        
        if not available_sig_fields:
            print("Aucun champ de signalisation trouve")
            return
        
        # Analyser différents critères de détection
        print(f"\nANALYSE DES CRITERES DE DETECTION:")
        
        signalization_examples = []
        
        # 1. Ex_type non vide
        if 'Ex_type' in sample.columns:
            ex_type_mask = sample['Ex_type'].notna() & (sample['Ex_type'] != '') & (sample['Ex_type'] != 0)
            ex_type_count = ex_type_mask.sum()
            print(f"  Ex_type non vide: {ex_type_count} lignes")
            
            if ex_type_count > 0:
                ex_type_values = sample[ex_type_mask]['Ex_type'].unique()
                print(f"  Valeurs Ex_type: {list(ex_type_values)}")
                
                # Collecter des exemples
                examples = sample[ex_type_mask].head(3)
                for idx, row in examples.iterrows():
                    example = {
                        'Identifiant cas': row.get('Identifiant cas', 'N/A'),
                        'Type': row.get('Type', 'N/A'),
                        'Ex_type': row.get('Ex_type', ''),
                        'Date_resolution_SIG': row.get('Date_resolution_SIG', ''),
                        'Ex_source': row.get('Ex_source', ''),
                        'Ex_famille': row.get('Ex_famille', ''),
                        'Ex_diagnostic': row.get('Ex_diagnostic', ''),
                        'Date_New_exp': row.get('Date_New_exp', '')
                    }
                    signalization_examples.append(example)
        
        # 2. Date_resolution_SIG non vide
        if 'Date_resolution_SIG' in sample.columns:
            date_sig_mask = sample['Date_resolution_SIG'].notna() & (sample['Date_resolution_SIG'] != '')
            date_sig_count = date_sig_mask.sum()
            print(f"  Date_resolution_SIG non vide: {date_sig_count} lignes")
        
        # 3. Au moins un champ Ex_* non vide
        ex_any_mask = pd.Series(False, index=sample.index)
        for field in available_sig_fields:
            if field.startswith('Ex_') or field == 'Date_resolution_SIG':
                field_mask = sample[field].notna() & (sample[field] != '') & (sample[field] != 0)
                ex_any_mask |= field_mask
        
        ex_any_count = ex_any_mask.sum()
        print(f"  Au moins un champ de signalisation non vide: {ex_any_count} lignes")
        
        # Afficher les exemples trouvés
        if signalization_examples:
            print(f"\n=== EXEMPLES DE DONNEES DE SIGNALISATION TROUVEES ===")
            for i, example in enumerate(signalization_examples[:5], 1):
                print(f"\nExemple {i}:")
                for key, value in example.items():
                    print(f"  {key}: {value}")
                    
            # Analyser les patterns
            print(f"\n=== ANALYSE DES PATTERNS ===")
            ex_types = [ex['Ex_type'] for ex in signalization_examples if ex['Ex_type']]
            if ex_types:
                print(f"Valeurs Ex_type trouvees: {list(set(ex_types))}")
            
            dates_sig = [ex['Date_resolution_SIG'] for ex in signalization_examples if ex['Date_resolution_SIG']]
            if dates_sig:
                print(f"Nombre de Date_resolution_SIG remplies: {len(dates_sig)}")
                
            sources = [ex['Ex_source'] for ex in signalization_examples if ex['Ex_source']]
            if sources:
                print(f"Valeurs Ex_source trouvees: {list(set(sources))}")
        else:
            print("\nAUCUN EXEMPLE DE SIGNALISATION TROUVE")
            print("Cela confirme que les champs Ex_* sont vides dans vos donnees")
            
    except Exception as e:
        print(f"ERREUR lors de l'analyse: {e}")

if __name__ == "__main__":
    analyze_signalization_in_real_file()