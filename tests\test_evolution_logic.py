#!/usr/bin/env python3
"""
Test de la nouvelle logique d'évolution SIGNALISATION -> EXPERTISE
"""

import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from signalization_duplication_logic import duplicate_signalization_tickets

def test_evolution_logic():
    """Test de la logique d'évolution avec données simulées"""
    
    print("=== TEST DE LA LOGIQUE D'ÉVOLUTION SIGNALISATION -> EXPERTISE ===\n")
    
    # Créer des données de test
    test_data = {
        'Identifiant cas': ['C12345', 'C67890', 'C11111', 'C22222'],
        'Ref OC': ['REF001', 'REF002', 'REF003', 'REF004'],
        'Type': ['EXPERTISE', 'EXPERTISE', 'EXPERTISE', 'EXPERTISE'],
        'Date ouverture du cas': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04'],
        'Date résolution': ['2024-01-15', '2024-01-20', '2024-01-25', '2024-01-30'],
        'Mois résolution': ['2024_01', '2024_01', '2024_01', '2024_01'],
        'Souce_OC': ['CLIENT', 'INTERNE', 'CLIENT', 'INTERNE'], 
        'Famille_OC': ['RACCORDEMENT', 'MAINTENANCE', 'RACCORDEMENT', 'MAINTENANCE'],
        'Diagnostic_OC': ['PB_TECH', 'PB_INFRA', 'PB_TECH', 'PB_INFRA'],
        
        # Champs d'évolution - seules les 2 premières lignes ont évolué
        'Ex_type': ['SIGNALISATION', 'SIGNALISATION', '', ''],  # Evolution pour C12345 et C67890
        'Ex_source': ['SIGNALISATION_SRC', 'AUTRE_SRC', '', ''],
        'Ex_famille': ['FAM_SIG', 'FAM_SIG2', '', ''],
        'Ex_diagnostic': ['DIAG_SIG', 'DIAG_SIG2', '', ''],
        'Date_resolution_SIG': ['2024-01-10', '2024-01-18', '', ''],
        'Date_New_exp': ['2024-01-12', '2024-01-19', '', ''],
        'Mois résol sig': ['2024_01', '2024_01', '', ''],
        'Mois ouv new exp': ['2024_01', '2024_01', '', ''],
        'Semaine ouv new exp': ['202402', '202403', '', ''],
        'Délai_JC2_Resol sig': [10, 16, '', ''],
        'Délai_JC3_Resol exp': [3, 1, '', '']
    }
    
    df_test = pd.DataFrame(test_data)
    
    print("DONNÉES INITIALES:")
    print(f"Nombre de lignes: {len(df_test)}")
    print("Lignes avec Ex_type non vide:")
    evolution_mask = df_test['Ex_type'].notna() & (df_test['Ex_type'] != '')
    for idx, row in df_test[evolution_mask].iterrows():
        print(f"  - {row['Identifiant cas']}: Ex_type='{row['Ex_type']}'")
    
    print(f"\nAVANT traitement:")
    print(df_test[['Identifiant cas', 'Type', 'Ex_type']].to_string())
    
    # Appliquer la logique d'évolution
    print(f"\n=== APPLICATION DE LA LOGIQUE D'ÉVOLUTION ===")
    df_result = duplicate_signalization_tickets(df_test)
    
    print(f"\nAPRÈS traitement:")
    print(f"Nombre de lignes: {len(df_result)} (était {len(df_test)})")
    
    # Afficher les résultats par identifiant
    for identifiant in df_result['Identifiant cas'].unique():
        subset = df_result[df_result['Identifiant cas'] == identifiant]
        print(f"\n--- Identifiant {identifiant} ---")
        print(subset[['Type', 'Date ouverture du cas', 'Date résolution', 'Souce_OC', 'Ex_type']].to_string())
        
    # Vérifications
    print(f"\n=== VÉRIFICATIONS ===")
    
    # Compter les types
    type_counts = df_result['Type'].value_counts()
    print("Répartition des types:")
    for type_name, count in type_counts.items():
        print(f"  {type_name}: {count}")
    
    # Vérifier que les lignes avec évolution ont été dédoublées
    expected_signalization = 2  # C12345 et C67890
    expected_expertise = 4      # 2 originales (C11111, C22222) + 2 évoluées (C12345, C67890)
    
    actual_signalization = (df_result['Type'] == 'SIGNALISATION').sum()
    actual_expertise = (df_result['Type'] == 'EXPERTISE').sum()
    
    print(f"\nAttendus: {expected_signalization} SIGNALISATION, {expected_expertise} EXPERTISE")
    print(f"Obtenus: {actual_signalization} SIGNALISATION, {actual_expertise} EXPERTISE")
    
    if actual_signalization == expected_signalization and actual_expertise == expected_expertise:
        print("✅ TEST RÉUSSI!")
    else:
        print("❌ TEST ÉCHOUÉ!")
        
    return df_result

def test_mapping_fields():
    """Test spécifique du mapping des champs"""
    
    print("\n=== TEST DU MAPPING DES CHAMPS ===")
    
    # Données avec tous les champs de mapping
    test_data = {
        'Identifiant cas': ['C99999'],
        'Ref OC': ['REF999'],
        'Type': ['EXPERTISE'],
        'Date ouverture du cas': ['2024-01-01'],
        'Date résolution': ['2024-01-15'],
        'Mois résolution': ['2024_01'],
        'Mois Ouv': ['2024_01'],
        'semaine ouv': ['202401'],
        'Souce_OC': ['ORIGINAL_SOURCE'],
        'Famille_OC': ['ORIGINAL_FAMILLE'],
        'Diagnostic_OC': ['ORIGINAL_DIAG'],
        'Délai résol JC': [15],
        
        # Champs d'évolution avec toutes les données
        'Ex_type': ['SIGNALISATION'],
        'Ex_source': ['EVOLUTION_SOURCE'], 
        'Ex_famille': ['EVOLUTION_FAMILLE'],
        'Ex_diagnostic': ['EVOLUTION_DIAG'],
        'Date_resolution_SIG': ['2024-01-10'],
        'Date_New_exp': ['2024-01-12'], 
        'Mois résol sig': ['2024_01'],
        'Mois ouv new exp': ['2024_01'],
        'Semaine ouv new exp': ['202402'],
        'Délai_JC2_Resol sig': [10],
        'Délai_JC3_Resol exp': [3]
    }
    
    df_test = pd.DataFrame(test_data)
    df_result = duplicate_signalization_tickets(df_test)
    
    print("VÉRIFICATION DU MAPPING:")
    
    # Trouver les lignes SIGNALISATION et EXPERTISE
    sig_row = df_result[df_result['Type'] == 'SIGNALISATION'].iloc[0]
    exp_row = df_result[df_result['Type'] == 'EXPERTISE'].iloc[0]
    
    print("\nLigne SIGNALISATION:")
    print(f"  Source_OC: {sig_row['Souce_OC']} (devrait être EVOLUTION_SOURCE)")
    print(f"  Famille_OC: {sig_row['Famille_OC']} (devrait être EVOLUTION_FAMILLE)")
    print(f"  Diagnostic_OC: {sig_row['Diagnostic_OC']} (devrait être EVOLUTION_DIAG)")
    print(f"  Date résolution: {sig_row['Date résolution']} (devrait être 2024-01-10)")
    
    print("\nLigne EXPERTISE:")
    print(f"  Date ouverture du cas: {exp_row['Date ouverture du cas']} (devrait être 2024-01-12)")
    print(f"  Source_OC: {exp_row['Souce_OC']} (devrait rester ORIGINAL_SOURCE)")
    print(f"  Famille_OC: {exp_row['Famille_OC']} (devrait rester ORIGINAL_FAMILLE)")
    
    # Vérifications
    mapping_ok = (
        sig_row['Souce_OC'] == 'EVOLUTION_SOURCE' and
        sig_row['Date résolution'] == '2024-01-10' and
        exp_row['Date ouverture du cas'] == '2024-01-12' and
        exp_row['Souce_OC'] == 'ORIGINAL_SOURCE'
    )
    
    if mapping_ok:
        print("\n✅ MAPPING CORRECT!")
    else:
        print("\n❌ PROBLÈME DE MAPPING!")
        
    return df_result

if __name__ == "__main__":
    # Test principal
    result1 = test_evolution_logic()
    
    # Test du mapping
    result2 = test_mapping_fields()
    
    print(f"\n=== RÉSUMÉ ===")
    print(f"Tests exécutés avec succès!")
    print(f"Nouvelle logique d'évolution SIGNALISATION -> EXPERTISE implémentée.")