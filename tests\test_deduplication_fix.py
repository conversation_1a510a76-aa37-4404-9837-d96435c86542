#!/usr/bin/env python3
"""
Test de la correction : vérifier que les doublons SIGNALISATION/EXPERTISE sont préservés
"""

import pandas as pd

def test_deduplication_logic():
    """Test de la logique de déduplication corrigée"""
    
    print("=== TEST DE LA CORRECTION DEDUPLICATION ===")
    
    # Simuler les données APRÈS dédoublement (comme dans le workflow réel)
    test_data = {
        'Identifiant cas': ['C34048200', 'C34048200', 'C12345', 'C67890'],
        'Type': ['SIGNALISATION', 'EXPERTISE', 'EXPERTISE', 'EXPERTISE'],
        'Date ouverture du cas': ['19/05/2025', '03/06/2025', '20/05/2025', '21/05/2025'],
        'Date résolution': ['01/06/2025', '05/06/2025', '06/06/2025', '07/06/2025'],
        'Souce_OC': ['SRC_SIG', 'OC', 'CLIENT', 'INTERNE'],
        'Ex_type': ['SIGNALISATION', 'SIGNALISATION', '', '']  # Trace du dédoublement
    }
    
    df = pd.DataFrame(test_data)
    
    print(f"DONNEES SIMULEES APRES DEDOUBLEMENT:")
    print(f"  Lignes totales: {len(df)}")
    
    for idx, row in df.iterrows():
        print(f"  {row['Identifiant cas']}: Type={row['Type']}")
    
    # Simuler l'ANCIENNE logique (problématique)
    print(f"\n=== ANCIENNE LOGIQUE (problématique) ===")
    df_old = df.copy()
    
    # Ancienne suppression: seulement par 'Identifiant cas'
    total_before = len(df_old)
    df_old.drop_duplicates(subset=['Identifiant cas'], keep='first', inplace=True)
    total_after = len(df_old)
    removed = total_before - total_after
    
    print(f"Suppression par 'Identifiant cas' uniquement:")
    print(f"  Avant: {total_before} lignes")
    print(f"  Après: {total_after} lignes")
    print(f"  Supprimées: {removed} lignes")
    
    print(f"Lignes restantes:")
    for idx, row in df_old.iterrows():
        print(f"  {row['Identifiant cas']}: Type={row['Type']}")
    
    if removed > 0:
        print("❌ PROBLEME: Doublons SIGNALISATION/EXPERTISE supprimés!")
    
    # Simuler la NOUVELLE logique (corrigée)
    print(f"\n=== NOUVELLE LOGIQUE (corrigée) ===")
    df_new = df.copy()
    
    # Nouvelle suppression: par ['Identifiant cas', 'Type']
    total_before = len(df_new)
    df_new.drop_duplicates(subset=['Identifiant cas', 'Type'], keep='first', inplace=True)
    total_after = len(df_new)
    removed = total_before - total_after
    
    print(f"Suppression par 'Identifiant cas' + 'Type':")
    print(f"  Avant: {total_before} lignes")
    print(f"  Après: {total_after} lignes") 
    print(f"  Supprimées: {removed} lignes")
    
    print(f"Lignes restantes:")
    for idx, row in df_new.iterrows():
        print(f"  {row['Identifiant cas']}: Type={row['Type']}")
    
    # Vérifications
    c34048200_count = (df_new['Identifiant cas'] == 'C34048200').sum()
    signalisation_count = (df_new['Type'] == 'SIGNALISATION').sum()
    expertise_count = (df_new['Type'] == 'EXPERTISE').sum()
    
    print(f"\nVERIFICATIONS:")
    print(f"  C34048200: {c34048200_count} lignes (attendu: 2)")
    print(f"  SIGNALISATION: {signalisation_count} lignes (attendu: 1)")
    print(f"  EXPERTISE: {expertise_count} lignes (attendu: 3)")
    
    success = (c34048200_count == 2 and signalisation_count == 1 and expertise_count == 3)
    
    if success:
        print("✅ CORRECTION REUSSIE! Doublons SIGNALISATION/EXPERTISE préservés")
        return True
    else:
        print("❌ Correction échouée")
        return False

def test_real_duplicates_removal():
    """Test que les vrais doublons sont toujours supprimés"""
    
    print(f"\n=== TEST SUPPRESSION VRAIS DOUBLONS ===")
    
    # Simuler des vrais doublons (même Identifiant cas + même Type)
    test_data = {
        'Identifiant cas': ['C11111', 'C11111', 'C22222'],  # C11111 en doublon
        'Type': ['EXPERTISE', 'EXPERTISE', 'EXPERTISE'],   # Même type
        'Date ouverture du cas': ['20/05/2025', '20/05/2025', '21/05/2025'],
        'Souce_OC': ['CLIENT', 'CLIENT', 'INTERNE']
    }
    
    df = pd.DataFrame(test_data)
    
    print(f"DONNEES AVEC VRAIS DOUBLONS:")
    print(f"  Lignes: {len(df)}")
    for idx, row in df.iterrows():
        print(f"  {row['Identifiant cas']}: Type={row['Type']}")
    
    # Appliquer la nouvelle logique
    df.drop_duplicates(subset=['Identifiant cas', 'Type'], keep='first', inplace=True)
    
    print(f"\nAPRES SUPPRESSION:")
    print(f"  Lignes: {len(df)}")
    for idx, row in df.iterrows():
        print(f"  {row['Identifiant cas']}: Type={row['Type']}")
    
    # Vérifications
    c11111_count = (df['Identifiant cas'] == 'C11111').sum()
    expected_total = 2  # C11111 (1) + C22222 (1)
    
    if len(df) == expected_total and c11111_count == 1:
        print("✅ Vrais doublons correctement supprimés")
        return True
    else:
        print("❌ Problème avec la suppression des vrais doublons")
        return False

if __name__ == "__main__":
    test1_ok = test_deduplication_logic()
    test2_ok = test_real_duplicates_removal()
    
    print(f"\n=== RESUME ===")
    if test1_ok and test2_ok:
        print("🎉 CORRECTION PARFAITEMENT FONCTIONNELLE!")
        print("✅ Doublons SIGNALISATION/EXPERTISE préservés")
        print("✅ Vrais doublons supprimés")
        print("\nVos tickets C34048200 apparaîtront maintenant dans le fichier final!")
    else:
        print("❌ Il reste des problèmes dans la logique")