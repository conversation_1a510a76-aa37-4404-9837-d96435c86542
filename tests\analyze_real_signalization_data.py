#!/usr/bin/env python3
"""
Analyse des vraies données pour comprendre la logique de signalisation
"""

import pandas as pd
import os

def analyze_signalization_in_real_file():
    """Analyser le fichier 300_FTTH_extract3.0.xlsx pour comprendre les données de signalisation"""
    
    # Essayer plusieurs chemins possibles
    possible_paths = [
        r"D:\donnees\FTTH\export\300_FTTH_extract3.0.xlsx",
        r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\300_FTTH_extract3.0.xlsx",
        r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\300_FTTH_extract3.0.xlsx"
    ]
    
    file_path = None
    for path in possible_paths:
        if os.path.exists(path):
            file_path = path
            print(f"FICHIER TROUVE: {path}")
            break
    
    if not file_path:
        print("FICHIER NON TROUVE dans les chemins suivants:")
        for path in possible_paths:
            print(f"  - {path}")
        return
        
    print("=== ANALYSE DES DONNEES REELLES DE SIGNALISATION ===")
    
    try:
        # Lire le fichier directement (pas de chunksize pour Excel)
        print("Chargement du fichier...")
        
        df = pd.read_excel(file_path)
        print(f"Fichier chargé: {len(df)} lignes, {len(df.columns)} colonnes")
        
        # Analyser par petits échantillons
        sample_size = min(1000, len(df))
        chunk = df.head(sample_size)
        print(f"Analyse d'un échantillon de {len(chunk)} lignes...")
        
        chunks_analyzed = 1
        signalization_examples = []
            print(f"Analyse de l'échantillon ({len(chunk)} lignes)...")
            
            # Champs de signalisation à analyser
            sig_fields = ['Date_resolution_SIG', 'Ex_type', 'Ex_source', 'Ex_famille', 'Ex_diagnostic', 
                         'Date_New_exp', 'Mois résol sig', 'Semaine résol sig', 'Délai_JC2_Resol sig', 
                         'Délai_JC3_Resol exp', 'Statut_SIG']
            
            available_sig_fields = [field for field in sig_fields if field in chunk.columns]
            print(f"  Champs de signalisation disponibles: {available_sig_fields}")
            
            if not available_sig_fields:
                print("  Aucun champ de signalisation trouvé dans ce chunk")
                continue
            
            # Analyser différents critères de détection
            print(f"\n  ANALYSE DES CRITÈRES DE DÉTECTION:")
            
            # 1. Ex_type non vide
            if 'Ex_type' in chunk.columns:
                ex_type_mask = chunk['Ex_type'].notna() & (chunk['Ex_type'] != '') & (chunk['Ex_type'] != 0)
                ex_type_count = ex_type_mask.sum()
                print(f"    Ex_type non vide: {ex_type_count} lignes")
                
                if ex_type_count > 0:
                    ex_type_values = chunk[ex_type_mask]['Ex_type'].unique()
                    print(f"    Valeurs Ex_type: {list(ex_type_values)}")
            
            # 2. Date_resolution_SIG non vide
            if 'Date_resolution_SIG' in chunk.columns:
                date_sig_mask = chunk['Date_resolution_SIG'].notna() & (chunk['Date_resolution_SIG'] != '')
                date_sig_count = date_sig_mask.sum()
                print(f"    Date_resolution_SIG non vide: {date_sig_count} lignes")
            
            # 3. Au moins un champ Ex_* non vide
            ex_any_mask = pd.Series(False, index=chunk.index)
            for field in available_sig_fields:
                if field.startswith('Ex_') or field == 'Date_resolution_SIG':
                    field_mask = chunk[field].notna() & (chunk[field] != '') & (chunk[field] != 0)
                    ex_any_mask |= field_mask
            
            ex_any_count = ex_any_mask.sum()
            print(f"    Au moins un champ de signalisation non vide: {ex_any_count} lignes")
            
            # Collecter quelques exemples
            if ex_any_count > 0:
                examples = chunk[ex_any_mask].head(3)
                for idx, row in examples.iterrows():
                    example = {
                        'Identifiant cas': row.get('Identifiant cas', 'N/A'),
                        'Type': row.get('Type', 'N/A'),
                        'Ex_type': row.get('Ex_type', ''),
                        'Date_resolution_SIG': row.get('Date_resolution_SIG', ''),
                        'Ex_source': row.get('Ex_source', ''),
                        'Date_New_exp': row.get('Date_New_exp', '')
                    }
                    signalization_examples.append(example)
        
        # Afficher les exemples trouvés
        if signalization_examples:
            print(f"\n=== EXEMPLES DE DONNÉES DE SIGNALISATION TROUVÉES ===")
            for i, example in enumerate(signalization_examples[:5], 1):
                print(f"\nExemple {i}:")
                for key, value in example.items():
                    print(f"  {key}: {value}")
                    
            # Analyser les patterns
            print(f"\n=== ANALYSE DES PATTERNS ===")
            ex_types = [ex['Ex_type'] for ex in signalization_examples if ex['Ex_type']]
            if ex_types:
                print(f"Valeurs Ex_type trouvées: {list(set(ex_types))}")
            
            dates_sig = [ex['Date_resolution_SIG'] for ex in signalization_examples if ex['Date_resolution_SIG']]
            if dates_sig:
                print(f"Nombre de Date_resolution_SIG remplies: {len(dates_sig)}")
                
            sources = [ex['Ex_source'] for ex in signalization_examples if ex['Ex_source']]
            if sources:
                print(f"Valeurs Ex_source trouvées: {list(set(sources))}")
        else:
            print("\nAUCUN EXEMPLE DE SIGNALISATION TROUVÉ")
            print("Cela confirme que les champs Ex_* sont vides dans vos données")
            
    except Exception as e:
        print(f"ERREUR lors de l'analyse: {e}")

def suggest_detection_logic():
    """Suggérer une logique de détection alternative"""
    
    print(f"\n=== SUGGESTIONS POUR LA LOGIQUE DE DÉTECTION ===")
    print("PROBLÈME IDENTIFIÉ:")
    print("  - Les champs Ex_* semblent être vides dans vos données réelles")
    print("  - La logique actuelle cherche Ex_type non vide")
    print("  - Donc aucune évolution n'est détectée")
    
    print(f"\nSOLUTIONS POSSIBLES:")
    print("1. LOGIQUE ALTERNATIVE 1: Détecter par Type")
    print("   - Si Type = 'SIGNALISATION' dans les données sources")
    print("   - Créer une ligne EXPERTISE correspondante")
    
    print("2. LOGIQUE ALTERNATIVE 2: Détecter par présence de champs spéciaux")
    print("   - Si Date_resolution_SIG non vide")
    print("   - Ou si Statut_SIG non vide")
    print("   - Créer le dédoublement")
    
    print("3. LOGIQUE ALTERNATIVE 3: Analyser manuellement quelques cas")
    print("   - Identifier manuellement des cas qui devraient être dédoublés")
    print("   - Comprendre le pattern commun")
    
    print(f"\nRECOMMANDATION:")
    print("Analysez manuellement votre fichier Excel pour identifier:")
    print("- Des lignes qui devraient être SIGNALISATION")
    print("- Quels champs permettent de les identifier")
    print("- La logique métier exacte d'évolution")

if __name__ == "__main__":
    analyze_signalization_in_real_file()
    suggest_detection_logic()