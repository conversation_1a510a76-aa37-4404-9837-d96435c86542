#!/usr/bin/env python3
"""
Test avec le filtre exact de l'utilisateur : Mois résolution = 2025_06 ET Ex_type = SIGNALISATION
"""

import pandas as pd
import os

def test_user_filter():
    file_path = r"D:\donnees\FTTH\export\300_FTTH_extract3.0.xlsx"
    
    if not os.path.exists(file_path):
        print("FICHIER NON TROUVE")
        return
        
    print("=== TEST AVEC FILTRE UTILISATEUR ===")
    print("Filtre: Mois résolution = '2025_06' ET Ex_type = 'SIGNALISATION'")
    
    try:
        # Lire le fichier complet (peut être long)
        print("Chargement du fichier complet...")
        df = pd.read_excel(file_path)
        print(f"Fichier chargé: {len(df)} lignes")
        
        # Vérifier les colonnes nécessaires
        required_cols = ['Mois résolution', 'Ex_type']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"ERREUR: Colonnes manquantes: {missing_cols}")
            print("Colonnes disponibles contenant 'Mois' ou 'Ex_':")
            for col in df.columns:
                if 'Mois' in col or 'Ex_' in col:
                    print(f"  {col}")
            return
        
        print("✅ Colonnes nécessaires trouvées")
        
        # Appliquer le filtre de l'utilisateur
        print("Application du filtre...")
        
        # Filtre 1: Mois résolution = 2025_06
        mois_filter = df['Mois résolution'] == '2025_06'
        mois_count = mois_filter.sum()
        print(f"  Lignes avec Mois résolution = '2025_06': {mois_count}")
        
        # Filtre 2: Ex_type = SIGNALISATION
        ex_type_filter = df['Ex_type'] == 'SIGNALISATION'
        ex_type_count = ex_type_filter.sum()
        print(f"  Lignes avec Ex_type = 'SIGNALISATION': {ex_type_count}")
        
        # Filtre combiné
        combined_filter = mois_filter & ex_type_filter
        combined_count = combined_filter.sum()
        print(f"  Lignes avec BOTH conditions: {combined_count}")
        
        if combined_count > 0:
            print(f"\n✅ TROUVÉ {combined_count} lignes correspondant au filtre!")
            
            # Analyser les lignes trouvées
            filtered_df = df[combined_filter]
            
            print(f"\nAnalyse des lignes trouvées:")
            for idx, row in filtered_df.head(3).iterrows():
                print(f"\n--- Ligne {idx} ---")
                print(f"Identifiant cas: {row.get('Identifiant cas', 'N/A')}")
                print(f"Type: {row.get('Type', 'N/A')}")
                print(f"Mois résolution: {row.get('Mois résolution', 'N/A')}")
                print(f"Ex_type: {row.get('Ex_type', 'N/A')}")
                print(f"Ex_source: {row.get('Ex_source', 'N/A')}")
                print(f"Ex_famille: {row.get('Ex_famille', 'N/A')}")
                print(f"Ex_diagnostic: {row.get('Ex_diagnostic', 'N/A')}")
                print(f"Date_resolution_SIG: {row.get('Date_resolution_SIG', 'N/A')}")
                print(f"Date_New_exp: {row.get('Date_New_exp', 'N/A')}")
            
            # Tester la logique de dédoublement sur ces données
            print(f"\n=== TEST DE DÉDOUBLEMENT ===")
            
            # Importer la fonction
            import sys
            sys.path.append(r"C:\Users\<USER>\Desktop\Python_projet\factu_stt_auto")
            from signalization_duplication_logic import duplicate_signalization_tickets
            
            print("Application de la logique de dédoublement...")
            result_df = duplicate_signalization_tickets(filtered_df)
            
            print(f"Résultat:")
            print(f"  Lignes avant: {len(filtered_df)}")
            print(f"  Lignes après: {len(result_df)}")
            
            if len(result_df) > len(filtered_df):
                print("✅ DÉDOUBLEMENT FONCTIONNE!")
                
                # Afficher les résultats
                print(f"\nLignes créées:")
                for idx, row in result_df.iterrows():
                    print(f"  {row.get('Identifiant cas', 'N/A')}: Type={row.get('Type', 'N/A')}")
            else:
                print("❌ Aucun dédoublement effectué")
                print("Vérification de la condition de détection...")
                
                evolution_mask = filtered_df['Ex_type'].notna() & (filtered_df['Ex_type'] != '') & (filtered_df['Ex_type'] != 0)
                print(f"Masque d'évolution: {evolution_mask.sum()} lignes détectées")
        else:
            print("❌ AUCUNE ligne trouvée avec le filtre combiné")
            
            if mois_count == 0:
                print("Problème: Aucune ligne avec Mois résolution = '2025_06'")
                unique_mois = df['Mois résolution'].unique()[:10]
                print(f"Valeurs Mois résolution disponibles: {list(unique_mois)}")
            
            if ex_type_count == 0:
                print("Problème: Aucune ligne avec Ex_type = 'SIGNALISATION'")
                unique_ex_type = df['Ex_type'].unique()[:10]
                print(f"Valeurs Ex_type disponibles: {list(unique_ex_type)}")
        
    except Exception as e:
        print(f"ERREUR: {e}")

if __name__ == "__main__":
    test_user_filter()