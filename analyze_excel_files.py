import pandas as pd
import os

# Chemins vers les fichiers Excel
ref_mut_path = r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\update_Base_manquant_REF_MUT.xlsx"
liste_oi_path = r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\Liste_OI.xlsx"

def analyze_excel_structure(file_path, file_name):
    """Analyse la structure d'un fichier Excel"""
    print(f"\n=== Analyse de {file_name} ===")
    print(f"Chemin: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"Fichier non trouvé: {file_path}")
        return
    
    try:
        # Lire toutes les feuilles
        excel_file = pd.ExcelFile(file_path)
        print(f"Feuilles disponibles: {excel_file.sheet_names}")
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- Fe<PERSON>le: {sheet_name} ---")
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"Dimensions: {df.shape}")
            print(f"Colonnes: {list(df.columns)}")
            
            # Afficher quelques exemples de données
            print("\nPremières lignes:")
            print(df.head(3).to_string())
            
            # Vérifier les valeurs manquantes
            missing_counts = df.isnull().sum()
            if missing_counts.sum() > 0:
                print(f"\nValeurs manquantes par colonne:")
                for col, count in missing_counts.items():
                    if count > 0:
                        print(f"  {col}: {count}")
                        
    except Exception as e:
        print(f"Erreur lors de l'analyse: {e}")

if __name__ == "__main__":
    # Analyser les fichiers
    analyze_excel_structure(ref_mut_path, "update_Base_manquant_REF_MUT.xlsx")
    analyze_excel_structure(liste_oi_path, "Liste_OI.xlsx")