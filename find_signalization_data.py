import pandas as pd
import os

def find_signalization_examples():
    """Trouver des exemples de lignes avec données de signalisation"""
    
    file_path = r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\300_FTTH_extract3.0.xlsx"
    
    if not os.path.exists(file_path):
        print(f"ERREUR: Fichier non trouve: {file_path}")
        return
    
    print("=== RECHERCHE D'EXEMPLES DE SIGNALISATION ===")
    
    try:
        # Lire le fichier par chunks pour éviter les timeouts
        chunk_size = 5000
        found_examples = []
        chunks_processed = 0
        
        for chunk in pd.read_excel(file_path, chunksize=chunk_size):
            chunks_processed += 1
            print(f"Analyse du chunk {chunks_processed} ({len(chunk)} lignes)...")
            
            # Champs de signalisation
            sig_fields = ['Date_resolution_SIG', 'Ex_type', 'Ex_source', 'Ex_famille', 'Ex_diagnostic']
            
            # Trouver les lignes avec au moins un champ de signalisation non vide
            sig_mask = chunk[sig_fields].notna().any(axis=1)
            sig_examples = chunk[sig_mask]
            
            if len(sig_examples) > 0:
                print(f"  TROUVE {len(sig_examples)} exemples de signalisation!")
                found_examples.append(sig_examples)
                
                # Analyser le premier exemple
                first_example = sig_examples.iloc[0]
                print(f"\n--- EXEMPLE DE TRANSFORMATION ---")
                print(f"Identifiant cas: {first_example.get('Identifiant cas', 'N/A')}")
                print(f"Ref OC: {first_example.get('Ref OC', 'N/A')}")
                print(f"OC: {first_example.get('OC', 'N/A')}")
                
                # Données actuelles (expertise)
                print(f"\nDONNEES EXPERTISE (actuelles):")
                expertise_fields = {
                    'Type': first_example.get('Type', 'N/A'),
                    'Date résolution': first_example.get('000_FTTH_tickets_STIT_ssDBL_FULL_Date de résolution', 'N/A'),
                    'Mois résolution': first_example.get('Mois résolution', 'N/A'),
                    'Source': first_example.get('Souce_OC', 'N/A'),
                    'Famille': first_example.get('Famille_OC', 'N/A'),
                    'Diagnostic': first_example.get('Diagnostic_OC', 'N/A')
                }
                for key, value in expertise_fields.items():
                    print(f"  {key}: {value}")
                
                # Données signalisation
                print(f"\nDONNEES SIGNALISATION (à extraire):")
                sig_data = {
                    'Date_resolution_SIG': first_example.get('Date_resolution_SIG', 'N/A'),
                    'Ex_type': first_example.get('Ex_type', 'N/A'),
                    'Ex_source': first_example.get('Ex_source', 'N/A'),
                    'Ex_famille': first_example.get('Ex_famille', 'N/A'),
                    'Ex_diagnostic': first_example.get('Ex_diagnostic', 'N/A'),
                    'Mois résol sig': first_example.get('Mois résol sig', 'N/A'),
                    'Statut_SIG': first_example.get('Statut_SIG', 'N/A')
                }
                for key, value in sig_data.items():
                    print(f"  {key}: {value}")
                    
                # Si on a trouvé des exemples, on s'arrête
                if len(found_examples) >= 1:
                    break
            
            # Limiter à 10 chunks pour éviter les timeouts
            if chunks_processed >= 10:
                break
        
        if found_examples:
            print(f"\n=== RESULTATS ===")
            total_sig_lines = sum(len(chunk) for chunk in found_examples)
            print(f"Total lignes avec signalisation trouvees: {total_sig_lines}")
            
            # Créer un mapping des champs
            print(f"\n=== MAPPING DES CHAMPS ===")
            mapping = {
                'EXPERTISE -> SIGNALISATION': {
                    'Type': 'Ex_type',
                    '000_FTTH_tickets_STIT_ssDBL_FULL_Date de résolution': 'Date_resolution_SIG', 
                    'Mois résolution': 'Mois résol sig',
                    'Souce_OC': 'Ex_source',
                    'Famille_OC': 'Ex_famille', 
                    'Diagnostic_OC': 'Ex_diagnostic'
                }
            }
            
            for direction, fields in mapping.items():
                print(f"\n{direction}:")
                for exp_field, sig_field in fields.items():
                    print(f"  {exp_field} -> {sig_field}")
        else:
            print(f"Aucun exemple de signalisation trouve dans les {chunks_processed} premiers chunks.")
            
    except Exception as e:
        print(f"ERREUR: {e}")

if __name__ == "__main__":
    find_signalization_examples()