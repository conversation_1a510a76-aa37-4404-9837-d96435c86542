#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test complet de génération Excel sans erreur
"""

import sys
import os
from pathlib import Path
import pandas as pd
import openpyxl
import numpy as np

# Ajouter le répertoire parent au PATH
sys.path.insert(0, str(Path(__file__).parent.parent))

def create_realistic_test_data():
    """Crée des données de test réalistes avec toutes les colonnes du vrai fichier"""
    
    # Simuler des données réalistes
    n_rows = 50  # 50 lignes de test
    
    data = {
        # Colonnes principales
        'N° de cas': [f"CAS{i:05d}" for i in range(1, n_rows + 1)],
        'Diagnostic_OC': [
            'STT001', 'STT002', None, 'AUTRE001', 'STT003',
            '', 'STT004', np.nan, 'AUTRE002', 'STT005'
        ] * 5,  # Répéter pour 50 lignes
        'Facturation éligible': [
            'OUI', 'NON', None, 'O<PERSON>', 'NON',
            '', 'O<PERSON>', np.nan, 'NON', 'O<PERSON>'
        ] * 5,
        '<PERSON><PERSON>gorie': [
            'Cat1', 'Cat2', None, 'Cat1', 'Cat3',
            '', 'Cat2', np.nan, 'Cat1', 'Cat3'
        ] * 5,
        'OI': ['OI1', 'OI2'] * 25,
        'OC': ['BOUYGUES', 'ORANGE', 'FREE'] * 17 + ['BOUYGUES'],  # Pour faire 50
        
        # Colonnes de date
        'Date ouverture du cas': pd.date_range('2025-01-01', periods=n_rows, freq='D'),
        'Date résolution': pd.date_range('2025-01-15', periods=n_rows, freq='D'),
        
        # Colonnes numériques
        'Délai_résolution': np.random.randint(1, 30, n_rows),
        'DEPT': [f"97{i%4 + 1}" for i in range(n_rows)],
        
        # Autres colonnes importantes
        'Référence facturable': [f"REF{i:04d}" if i % 3 == 0 else None for i in range(n_rows)],
        'Montant': np.random.uniform(10.0, 1000.0, n_rows),
    }
    
    return pd.DataFrame(data)

def test_main_function():
    """Test de la fonction principale avec données réalistes"""
    try:
        print("[TEST] Création de données de test réalistes...")
        df_test = create_realistic_test_data()
        
        print(f"[INFO] {len(df_test)} lignes × {len(df_test.columns)} colonnes créées")
        print(f"[INFO] Colonnes: {list(df_test.columns)}")
        
        # Importer les fonctions principales
        from code_export_facturation_STT_V6 import create_summary_sheet, create_summary_sheet_for_group
        
        # Créer un fichier Excel de test complet
        test_file = Path("test_generation_complete.xlsx")
        
        # Créer le fichier avec ExcelWriter comme dans le code principal
        print("[TEST] Création du fichier Excel avec ExcelWriter...")
        with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
            # Remplacer les NaN par des chaînes vides
            for col in df_test.columns:
                if df_test[col].dtype == 'object' or pd.api.types.is_string_dtype(df_test[col]):
                    df_test[col] = df_test[col].fillna('')
            
            # Exporter les données principales (copier la logique du code principal)
            df_test.to_excel(writer, sheet_name='Data', index=False)
            
            # Créer une feuille Rapport manuelle comme dans le code
            writer.book.create_sheet('Rapport')
            worksheet = writer.book['Rapport']
            
            # Écrire les en-têtes cellule par cellule (comme dans le code corrigé)
            headers = list(df_test.columns)
            for col_idx, header in enumerate(headers, 1):
                try:
                    clean_header = str(header) if header is not None else f"Col_{col_idx}"
                    worksheet.cell(row=1, column=col_idx, value=clean_header)
                except Exception as e:
                    print(f"[WARNING] Erreur écriture en-tête col {col_idx}: {str(e)}")
                    worksheet.cell(row=1, column=col_idx, value=f"Col_{col_idx}")

            # Écrire les données cellule par cellule
            for row_idx, (_, row) in enumerate(df_test.iterrows(), 2):
                for col_idx, col_name in enumerate(headers, 1):
                    try:
                        raw_value = row[col_name]
                        
                        if pd.isna(raw_value) or raw_value == 'nan' or raw_value == '':
                            clean_value = ""
                        elif isinstance(raw_value, (int, float)):
                            if pd.isna(raw_value):
                                clean_value = ""
                            else:
                                clean_value = float(raw_value) if isinstance(raw_value, float) else int(raw_value)
                        else:
                            clean_value = str(raw_value) if raw_value is not None else ""
                        
                        worksheet.cell(row=row_idx, column=col_idx, value=clean_value)
                        
                    except Exception as e:
                        print(f"[WARNING] Erreur écriture ligne {row_idx}, col {col_idx}: {str(e)}")
                        worksheet.cell(row=row_idx, column=col_idx, value="")
        
        print("[TEST] Fichier Excel de base créé avec succès")
        
        # Maintenant tester la fonction create_summary_sheet
        print("[TEST] Ajout de la feuille de synthèse...")
        create_summary_sheet(test_file, df_test)
        
        # Vérifier que le fichier s'ouvre sans erreur
        print("[TEST] Vérification de l'ouverture du fichier...")
        wb_test = openpyxl.load_workbook(test_file)
        
        print(f"[INFO] Feuilles présentes: {wb_test.sheetnames}")
        
        # Vérifier chaque feuille
        for sheet_name in wb_test.sheetnames:
            ws = wb_test[sheet_name]
            print(f"[INFO] Feuille '{sheet_name}': {ws.max_row} lignes × {ws.max_column} colonnes")
            
            # Vérifier qu'il n'y a pas de valeurs problématiques
            problem_cells = []
            for row in range(1, min(ws.max_row + 1, 10)):  # Vérifier les 10 premières lignes
                for col in range(1, ws.max_column + 1):
                    cell_value = ws.cell(row=row, column=col).value
                    if cell_value is not None:
                        str_value = str(cell_value)
                        if str_value in ['nan', 'inf', '-inf'] or 'ERROR' in str_value.upper():
                            problem_cells.append(f"{sheet_name}[{row},{col}]: {cell_value}")
            
            if problem_cells:
                print(f"[WARNING] Cellules problématiques dans {sheet_name}:")
                for cell in problem_cells[:5]:  # Afficher max 5 exemples
                    print(f"  {cell}")
            else:
                print(f"[OK] Aucune cellule problématique dans {sheet_name}")
        
        wb_test.close()
        
        print("[SUCCESS] Test de génération complète réussi!")
        print("Le fichier Excel devrait s'ouvrir sans message d'erreur.")
        
        return True
        
    except Exception as e:
        print(f"[ERREUR] Test échoué: {str(e)}")
        import traceback
        print(f"[DEBUG] {traceback.format_exc()}")
        return False
    finally:
        # Nettoyer
        if 'test_file' in locals() and test_file.exists():
            test_file.unlink()

def main():
    print("=== TEST DE GENERATION COMPLETE ===")
    success = test_main_function()
    
    if success:
        print("\n[SUCCESS] Le code principal devrait maintenant générer des fichiers Excel parfaits!")
    else:
        print("\n[FAIL] Des problèmes persistent dans la génération Excel.")

if __name__ == "__main__":
    main()