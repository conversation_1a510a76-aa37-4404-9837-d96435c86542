#!/usr/bin/env python3
"""
Test FINAL pour débugger le dédoublement SIGNALISATION
Utilise les vraies données pour identifier le problème exact.
"""

import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from signalization_duplication_logic import duplicate_signalization_tickets

def test_real_data_signalization():
    """Test avec les vraies données du fichier extract"""
    print("=== TEST DÉDOUBLEMENT AVEC VRAIES DONNÉES ===")
    
    # Charger le fichier extract réel
    extract_path = r'C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_06\300_FTTH_extract3.0.xlsx'
    
    try:
        df_extract = pd.read_excel(extract_path)
        print(f"Extract chargé: {len(df_extract)} lignes")
        
        # Filtrer uniquement les lignes avec Ex_type='SIGNALISATION'
        if 'Ex_type' in df_extract.columns:
            signalization_lines = df_extract[df_extract['Ex_type'] == 'SIGNALISATION'].copy()
            print(f"Lignes Ex_type='SIGNALISATION': {len(signalization_lines)}")
            
            if len(signalization_lines) > 0:
                # Prendre les 5 premières lignes pour test
                test_df = signalization_lines.head(5).copy()
                
                print("\n=== DONNÉES DE TEST ===")
                for idx, row in test_df.iterrows():
                    identifiant = row['Identifiant cas']
                    type_val = row.get('Type', 'N/A')
                    ex_type = row.get('Ex_type', 'N/A')
                    print(f"{identifiant}: Type={type_val}, Ex_type={ex_type}")
                
                print(f"\n=== TEST DÉDOUBLEMENT ===")
                print(f"AVANT: {len(test_df)} lignes")
                
                # Vérifier les colonnes requises
                required_cols = ['Ex_type', 'Ex_source', 'Ex_famille', 'Ex_diagnostic', 'Date_resolution_SIG', 'Date_New_exp']
                available_cols = [col for col in required_cols if col in test_df.columns]
                print(f"Colonnes signalisation disponibles: {available_cols}")
                
                # Appliquer le dédoublement
                result_df = duplicate_signalization_tickets(test_df)
                
                print(f"APRÈS: {len(result_df)} lignes")
                print(f"Différence: +{len(result_df) - len(test_df)} lignes")
                
                if len(result_df) > len(test_df):
                    print("\n✅ DÉDOUBLEMENT RÉUSSI!")
                    
                    # Vérifier les Types
                    if 'Type' in result_df.columns:
                        type_counts = result_df['Type'].value_counts()
                        print(f"Répartition des Types: {dict(type_counts)}")
                    
                    # Vérifier C34048200 spécifiquement
                    if 'Identifiant cas' in result_df.columns:
                        c34_lines = result_df[result_df['Identifiant cas'] == 'C34048200']
                        if len(c34_lines) > 0:
                            print(f"\n🎯 C34048200: {len(c34_lines)} ligne(s)")
                            for idx, row in c34_lines.iterrows():
                                print(f"  Type: {row.get('Type', 'N/A')}")
                else:
                    print("\n❌ DÉDOUBLEMENT ÉCHOUÉ!")
                    
                    # Débugger pourquoi
                    print("\nDébogage:")
                    if 'Ex_type' not in test_df.columns:
                        print("- Ex_type manquant")
                    else:
                        ex_sig_count = (test_df['Ex_type'] == 'SIGNALISATION').sum()
                        print(f"- Lignes Ex_type='SIGNALISATION': {ex_sig_count}")
                        
                        if ex_sig_count == 0:
                            unique_ex_types = test_df['Ex_type'].unique()
                            print(f"- Valeurs Ex_type présentes: {unique_ex_types}")
                            
            else:
                print("❌ Aucune ligne Ex_type='SIGNALISATION' trouvée!")
        else:
            print("❌ Colonne Ex_type manquante!")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

def test_workflow_integration():
    """Test d'intégration complète pour simuler le workflow réel"""
    print("\n\n=== TEST INTÉGRATION WORKFLOW ===")
    
    # Simuler des données comme dans le workflow réel
    test_data = {
        'Identifiant cas': ['C34048200', 'C12345', 'C67890'],
        'Type': ['EXPERTISE', 'EXPERTISE', 'SIGNALISATION'],  # Types variés
        'Ex_type': ['SIGNALISATION', '', ''],  # Seul C34048200 a Ex_type
        'Ex_source': ['OI', '', ''],
        'Ex_famille': ['FAM_TEST', '', ''],
        'Ex_diagnostic': ['DIAG_TEST', '', ''],
        'Date_resolution_SIG': ['2025-06-01', '', ''],
        'Date_New_exp': ['2025-06-04', '', ''],
        'Souce_OC': ['SRC_ORIG', 'SRC_B', 'SRC_C'],
        'Famille_OC': ['FAM_ORIG', 'FAM_B', 'FAM_C'],
        'Diagnostic_OC': ['DIAG_ORIG', 'DIAG_B', 'DIAG_C'],
        'Date résolution': ['2025-06-05', '2025-06-02', '2025-06-03'],
        'Mois résolution': ['2025-06', '2025-06', '2025-06']
    }
    
    df_test = pd.DataFrame(test_data)
    
    print(f"Données simulées: {len(df_test)} lignes")
    print("Identifiants:")
    for idx, row in df_test.iterrows():
        print(f"  {row['Identifiant cas']}: Type={row['Type']}, Ex_type='{row['Ex_type']}'")
    
    # Test dédoublement
    print(f"\nAvant dédoublement: {len(df_test)} lignes")
    result_df = duplicate_signalization_tickets(df_test)
    print(f"Après dédoublement: {len(result_df)} lignes")
    
    if len(result_df) > len(df_test):
        print("\n✅ SUCCÈS!")
        # Vérifier les résultats
        c34_lines = result_df[result_df['Identifiant cas'] == 'C34048200']
        print(f"C34048200: {len(c34_lines)} ligne(s)")
        
        for idx, row in c34_lines.iterrows():
            print(f"  Type: {row['Type']}, Date résolution: {row['Date résolution']}")
            
        # Test suppression doublons (comme dans le code principal)
        print(f"\nTest suppression doublons:")
        before_dedup = len(result_df)
        result_df.drop_duplicates(subset=['Identifiant cas', 'Type'], keep='first', inplace=True)
        after_dedup = len(result_df)
        removed = before_dedup - after_dedup
        
        print(f"Avant: {before_dedup}, Après: {after_dedup}, Supprimées: {removed}")
        
        final_c34 = result_df[result_df['Identifiant cas'] == 'C34048200']
        print(f"C34048200 final: {len(final_c34)} ligne(s)")
        
    else:
        print("❌ ÉCHEC - Pas de dédoublement!")

if __name__ == "__main__":
    test_real_data_signalization()
    test_workflow_integration()