#!/usr/bin/env python3
"""
Analyse rapide des données de signalisation
"""

import pandas as pd
import os

def quick_analyze():
    file_path = r"D:\donnees\FTTH\export\300_FTTH_extract3.0.xlsx"
    
    if not os.path.exists(file_path):
        print("FICHIER NON TROUVE")
        return
        
    print("=== ANALYSE RAPIDE ===")
    
    try:
        # Lire seulement les 100 premières lignes
        print("Chargement des 100 premieres lignes...")
        df = pd.read_excel(file_path, nrows=100)
        print(f"Echantillon charge: {len(df)} lignes")
        
        # Chercher les champs de signalisation
        sig_fields = ['Ex_type', 'Date_resolution_SIG', 'Ex_source', 'Ex_famille', 'Ex_diagnostic']
        
        print(f"\nColonnes disponibles:")
        for col in df.columns:
            if any(sig in col for sig in ['Ex_', 'SIG', 'resolution_SIG']):
                print(f"  {col}")
        
        print(f"\nAnalyse des champs de signalisation:")
        
        for field in sig_fields:
            if field in df.columns:
                non_empty = (df[field].notna() & (df[field] != '') & (df[field] != 0)).sum()
                print(f"  {field}: {non_empty} valeurs non vides")
                
                if non_empty > 0:
                    # Montrer quelques valeurs
                    values = df[df[field].notna() & (df[field] != '')][field].head(3).tolist()
                    print(f"    Exemples: {values}")
            else:
                print(f"  {field}: COLONNE NON TROUVEE")
                
    except Exception as e:
        print(f"ERREUR: {e}")

if __name__ == "__main__":
    quick_analyze()