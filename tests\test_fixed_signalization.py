#!/usr/bin/env python3
"""
Test de la logique corrigée : Ex_type = 'SIGNALISATION'
"""

import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from signalization_duplication_logic import duplicate_signalization_tickets

def test_fixed_logic():
    """Test avec la nouvelle logique Ex_type = 'SIGNALISATION'"""
    
    print("=== TEST LOGIQUE CORRIGEE ===")
    
    # Simuler des données avec Ex_type = 'SIGNALISATION'
    test_data = {
        'Identifiant cas': ['C34048200', 'C12345', 'C67890'],
        'Ref OC': ['ISA00120250519008803', 'REF001', 'REF002'],
        'Type': ['EXPERTISE', 'EXPERTISE', 'EXPERTISE'], 
        'Date ouverture du cas': ['19/05/2025', '20/05/2025', '21/05/2025'],
        'Date résolution': ['05/06/2025', '06/06/2025', '07/06/2025'],
        'Mois résolution': ['2025_06', '2025_06', '2025_06'],
        'Souce_OC': ['OC', 'CLIENT', 'INTERNE'],
        'Famille_OC': ['PM-PBO', 'RACCORDEMENT', 'MAINTENANCE'],
        'Diagnostic_OC': ['STT01#PAS DE DEFAUT CONSTATE S', 'DIAG1', 'DIAG2'],
        
        # Champs Ex_* - première ligne avec SIGNALISATION, autres vides
        'Ex_type': ['SIGNALISATION', '', ''],  # Seule la première sera dédoublée
        'Ex_source': ['SRC_SIG', '', ''],
        'Ex_famille': ['FAM_SIG', '', ''],
        'Ex_diagnostic': ['DIAG_SIG', '', ''],
        'Date_resolution_SIG': ['01/06/2025', '', ''],
        'Date_New_exp': ['03/06/2025', '', '']
    }
    
    df = pd.DataFrame(test_data)
    
    print(f"DONNEES INITIALES:")
    print(f"  Lignes: {len(df)}")
    for idx, row in df.iterrows():
        print(f"  {row['Identifiant cas']}: Type={row['Type']}, Ex_type='{row['Ex_type']}'")
    
    # Vérifier la condition de détection
    evolution_mask = (df['Ex_type'] == 'SIGNALISATION')
    detected_count = evolution_mask.sum()
    print(f"\nDETECTION:")
    print(f"  Lignes avec Ex_type='SIGNALISATION': {detected_count}")
    
    if detected_count > 0:
        detected_cases = df[evolution_mask]['Identifiant cas'].tolist()
        print(f"  Cas détectés: {detected_cases}")
    
    # Appliquer la logique de dédoublement
    print(f"\nAPPLICATION DU DEDOUBLEMENT:")
    result_df = duplicate_signalization_tickets(df)
    
    print(f"\nRESULTAT:")
    print(f"  Lignes avant: {len(df)}")
    print(f"  Lignes après: {len(result_df)}")
    
    if len(result_df) > len(df):
        print("✅ DEDOUBLEMENT REUSSI!")
        
        # Analyser les résultats par cas
        for identifiant in result_df['Identifiant cas'].unique():
            case_rows = result_df[result_df['Identifiant cas'] == identifiant]
            print(f"\n--- Cas {identifiant} ---")
            for idx, row in case_rows.iterrows():
                print(f"  Type: {row['Type']}, Date ouv: {row.get('Date ouverture du cas', 'N/A')}, Date res: {row.get('Date résolution', 'N/A')}")
        
        # Vérifier les counts
        type_counts = result_df['Type'].value_counts()
        print(f"\nREPARTITION FINALE:")
        for type_name, count in type_counts.items():
            print(f"  {type_name}: {count}")
            
        # Vérifications attendues
        expected_signalisation = 1  # C34048200
        expected_expertise = 3     # C12345, C67890 (inchangés) + C34048200 (évolution)
        
        actual_signalisation = (result_df['Type'] == 'SIGNALISATION').sum()
        actual_expertise = (result_df['Type'] == 'EXPERTISE').sum()
        
        print(f"\nVERIFICATION:")
        print(f"  Attendu: {expected_signalisation} SIGNALISATION, {expected_expertise} EXPERTISE")
        print(f"  Obtenu: {actual_signalisation} SIGNALISATION, {actual_expertise} EXPERTISE")
        
        if actual_signalisation == expected_signalisation and actual_expertise == expected_expertise:
            print("✅ TEST PARFAITEMENT REUSSI!")
            return True
        else:
            print("❌ Nombres incorrects")
            return False
    else:
        print("❌ Aucun dédoublement effectué")
        return False

def test_no_signalization():
    """Test avec aucune ligne Ex_type = 'SIGNALISATION'"""
    
    print(f"\n=== TEST SANS SIGNALISATION ===")
    
    # Données sans Ex_type = 'SIGNALISATION'
    test_data = {
        'Identifiant cas': ['C11111', 'C22222'],
        'Type': ['EXPERTISE', 'EXPERTISE'],
        'Ex_type': ['', 'AUTRE']  # Pas de 'SIGNALISATION'
    }
    
    df = pd.DataFrame(test_data)
    result_df = duplicate_signalization_tickets(df)
    
    print(f"Lignes avant: {len(df)}")
    print(f"Lignes après: {len(result_df)}")
    
    if len(result_df) == len(df):
        print("✅ Aucun dédoublement (attendu)")
        return True
    else:
        print("❌ Dédoublement inattendu")
        return False

if __name__ == "__main__":
    test1_ok = test_fixed_logic()
    test2_ok = test_no_signalization()
    
    print(f"\n=== RESUME ===")
    print(f"Test avec SIGNALISATION: {'✅ REUSSI' if test1_ok else '❌ ECHEC'}")
    print(f"Test sans SIGNALISATION: {'✅ REUSSI' if test2_ok else '❌ ECHEC'}")
    
    if test1_ok and test2_ok:
        print(f"\n🎉 LOGIQUE CORRIGEE FONCTIONNE PARFAITEMENT!")
        print(f"La détection Ex_type='SIGNALISATION' est opérationnelle.")
    else:
        print(f"\n❌ Il reste des problèmes à corriger.")