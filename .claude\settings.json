{"env": {"CLAUDE_FLOW_AUTO_COMMIT": "false", "CLAUDE_FLOW_AUTO_PUSH": "false", "CLAUDE_FLOW_HOOKS_ENABLED": "true", "CLAUDE_FLOW_TELEMETRY_ENABLED": "true", "CLAUDE_FLOW_REMOTE_EXECUTION": "true", "CLAUDE_FLOW_CHECKPOINTS_ENABLED": "true"}, "permissions": {"allow": ["Bash(npx claude-flow *)", "Bash(npm run lint)", "Bash(npm run test:*)", "Bash(npm test *)", "Bash(git status)", "<PERSON><PERSON>(git diff *)", "Bash(git log *)", "Bash(git add *)", "<PERSON><PERSON>(git commit *)", "<PERSON><PERSON>(git push)", "<PERSON>sh(git config *)", "Bash(git tag *)", "<PERSON><PERSON>(git branch *)", "<PERSON><PERSON>(git checkout *)", "<PERSON>sh(git stash *)", "<PERSON><PERSON>(jq *)", "<PERSON><PERSON>(node *)", "Ba<PERSON>(which *)", "Bash(pwd)", "Bash(ls *)"], "deny": ["Bash(rm -rf /)", "Bash(curl * | bash)", "Bash(wget * | sh)", "<PERSON><PERSON>(eval *)"]}, "hooks": {"PreToolUse": [{"matcher": "<PERSON><PERSON>", "hooks": [{"type": "command", "command": "cat | jq -r '.tool_input.command // empty' | tr '\\n' '\\0' | xargs -0 -I {} npx claude-flow@alpha hooks pre-command --command '{}' --validate-safety true --prepare-resources true"}]}, {"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "cat | jq -r '.tool_input.file_path // .tool_input.path // empty' | tr '\\n' '\\0' | xargs -0 -I {} npx claude-flow@alpha hooks pre-edit --file '{}' --auto-assign-agents true --load-context true"}]}], "PostToolUse": [{"matcher": "<PERSON><PERSON>", "hooks": [{"type": "command", "command": "cat | jq -r '.tool_input.command // empty' | tr '\\n' '\\0' | xargs -0 -I {} npx claude-flow@alpha hooks post-command --command '{}' --track-metrics true --store-results true"}]}, {"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "cat | jq -r '.tool_input.file_path // .tool_input.path // empty' | tr '\\n' '\\0' | xargs -0 -I {} npx claude-flow@alpha hooks post-edit --file '{}' --format true --update-memory true"}]}], "PreCompact": [{"matcher": "manual", "hooks": [{"type": "command", "command": "/bin/bash -c 'INPUT=$(cat); CUSTOM=$(echo \"$INPUT\" | jq -r \".custom_instructions // \\\"\\\"\"); echo \"🔄 PreCompact Guidance:\"; echo \"📋 IMPORTANT: Review CLAUDE.md in project root for:\"; echo \"   • 54 available agents and concurrent usage patterns\"; echo \"   • Swarm coordination strategies (hierarchical, mesh, adaptive)\"; echo \"   • SPARC methodology workflows with batchtools optimization\"; echo \"   • Critical concurrent execution rules (GOLDEN RULE: 1 MESSAGE = ALL OPERATIONS)\"; if [ -n \"$CUSTOM\" ]; then echo \"🎯 Custom compact instructions: $CUSTOM\"; fi; echo \"✅ Ready for compact operation\"'"}]}, {"matcher": "auto", "hooks": [{"type": "command", "command": "/bin/bash -c 'echo \"🔄 Auto-Compact Guidance (Context Window Full):\"; echo \"📋 CRITICAL: Before compacting, ensure you understand:\"; echo \"   • All 54 agents available in .claude/agents/ directory\"; echo \"   • Concurrent execution patterns from CLAUDE.md\"; echo \"   • Batchtools optimization for 300% performance gains\"; echo \"   • Swarm coordination strategies for complex tasks\"; echo \"⚡ Apply GOLDEN RULE: Always batch operations in single messages\"; echo \"✅ Auto-compact proceeding with full agent context\"'"}]}], "Stop": [{"hooks": [{"type": "command", "command": "npx claude-flow@alpha hooks session-end --generate-summary true --persist-state true --export-metrics true"}]}]}, "includeCoAuthoredBy": true, "enabledMcpjsonServers": ["claude-flow", "ruv-swarm"]}