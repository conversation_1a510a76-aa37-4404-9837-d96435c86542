#!/usr/bin/env python3
"""
Rechercher des exemples avec Ex_type rempli dans le fichier
"""

import pandas as pd
import os

def find_signalization_examples():
    file_path = r"D:\donnees\FTTH\export\300_FTTH_extract3.0.xlsx"
    
    if not os.path.exists(file_path):
        print("FICHIER NON TROUVE")
        return
        
    print("=== RECHERCHE D'EXEMPLES AVEC Ex_type REMPLI ===")
    
    try:
        # Stratégie: lire par petits chunks pour trouver des exemples
        chunk_size = 1000
        max_chunks = 50  # Limiter pour éviter timeout
        found_examples = []
        
        print(f"Recherche dans les {max_chunks * chunk_size} premieres lignes...")
        
        # Lecture chunk par chunk
        for i in range(max_chunks):
            start_row = i * chunk_size
            
            try:
                chunk = pd.read_excel(file_path, skiprows=start_row, nrows=chunk_size)
                
                if chunk.empty:
                    break
                
                print(f"Chunk {i+1}: lignes {start_row} à {start_row + len(chunk)}")
                
                # Chercher Ex_type non vide
                if 'Ex_type' in chunk.columns:
                    ex_type_mask = chunk['Ex_type'].notna() & (chunk['Ex_type'] != '') & (chunk['Ex_type'] != 0)
                    ex_type_count = ex_type_mask.sum()
                    
                    if ex_type_count > 0:
                        print(f"  TROUVE {ex_type_count} exemples avec Ex_type rempli!")
                        
                        # Collecter les exemples
                        examples = chunk[ex_type_mask]
                        for idx, row in examples.head(3).iterrows():
                            example = {
                                'Ligne_approx': start_row + idx,
                                'Identifiant cas': row.get('Identifiant cas', 'N/A'),
                                'Type': row.get('Type', 'N/A'),
                                'Ex_type': row.get('Ex_type', ''),
                                'Date_resolution_SIG': row.get('Date_resolution_SIG', ''),
                                'Ex_source': row.get('Ex_source', ''),
                                'Ex_famille': row.get('Ex_famille', ''),
                                'Ex_diagnostic': row.get('Ex_diagnostic', ''),
                                'Date_New_exp': row.get('Date_New_exp', '')
                            }
                            found_examples.append(example)
                        
                        if len(found_examples) >= 5:  # Assez d'exemples
                            break
                else:
                    print(f"  Ex_type non trouve dans ce chunk")
                    
            except Exception as e:
                print(f"  Erreur chunk {i+1}: {e}")
                continue
        
        # Afficher les résultats
        if found_examples:
            print(f"\n=== {len(found_examples)} EXEMPLES TROUVES ===")
            for i, example in enumerate(found_examples, 1):
                print(f"\nExemple {i} (ligne ~{example['Ligne_approx']}):")
                for key, value in example.items():
                    if key != 'Ligne_approx':
                        print(f"  {key}: {value}")
        else:
            print(f"\n❌ AUCUN EXEMPLE TROUVE avec Ex_type rempli")
            print("Cela confirme que le probleme vient de donnees vides")
            
            # Suggestion
            print(f"\nSOLUTIONS POSSIBLES:")
            print("1. Les donnees Ex_* sont effectivement vides dans ce fichier")
            print("2. La logique de detection doit etre changee") 
            print("3. Il faut chercher un autre critere d'identification")
            
    except Exception as e:
        print(f"ERREUR: {e}")

if __name__ == "__main__":
    find_signalization_examples()