#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple de la génération Excel corrigée
"""

import sys
from pathlib import Path
import pandas as pd
import openpyxl
import numpy as np

# Ajouter le répertoire parent au PATH
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_simple():
    """Test simple avec peu de données"""
    try:
        print("[TEST] Test de génération Excel simple...")
        
        # Données de test simples et cohérentes
        data = {
            'Diagnostic_OC': ['STT001', 'STT002', 'AUTRE001', 'STT003', 'AUTRE002'],
            'Facturation éligible': ['OUI', 'NON', 'OUI', 'NON', 'OUI'],
            'Catégorie': ['Cat1', 'Cat1', 'Cat2', 'Cat1', 'Cat2'],
            'OI': ['OI1', 'OI1', 'OI2', 'OI1', 'OI2'],
            'OC': ['BOUYGUES', 'ORANGE', 'FREE', 'BOUYGUES', 'ORANGE'],
            'Date ouverture du cas': ['2025-01-01', '2025-01-02', '2025-01-03', '2025-01-04', '2025-01-05'],
            'Date résolution': ['2025-01-15', '2025-01-16', '2025-01-17', '2025-01-18', '2025-01-19'],
            'Délai_résolution': [14, 14, 14, 14, 14],
            'DEPT': ['971', '972', '973', '974', '971']
        }
        
        df_test = pd.DataFrame(data)
        print(f"[INFO] DataFrame créé: {len(df_test)} lignes × {len(df_test.columns)} colonnes")
        
        # Test du code principal
        from code_export_facturation_STT_V6 import create_summary_sheet
        
        # Créer un fichier Excel basique
        test_file = Path("test_simple.xlsx")
        
        # Créer avec openpyxl directement
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = 'Rapport'
        
        # Écrire les données basiques
        headers = list(df_test.columns)
        for col_idx, header in enumerate(headers, 1):
            ws.cell(row=1, column=col_idx, value=str(header))
        
        for row_idx, (_, row) in enumerate(df_test.iterrows(), 2):
            for col_idx, (col_name, value) in enumerate(row.items(), 1):
                ws.cell(row=row_idx, column=col_idx, value=str(value) if value is not None else "")
        
        wb.save(test_file)
        print("[INFO] Fichier Excel basique créé")
        
        # Tester create_summary_sheet
        print("[TEST] Test de create_summary_sheet...")
        create_summary_sheet(test_file, df_test)
        
        # Vérifier le résultat
        wb_result = openpyxl.load_workbook(test_file)
        print(f"[INFO] Feuilles après synthèse: {wb_result.sheetnames}")
        
        if 'Synthèse Globale' in wb_result.sheetnames:
            ws_synth = wb_result['Synthèse Globale']
            print(f"[INFO] Synthèse: {ws_synth.max_row} lignes × {ws_synth.max_column} colonnes")
            
            # Vérifier quelques cellules importantes
            title = ws_synth.cell(row=1, column=1).value
            print(f"[INFO] Titre: {title}")
            
            # Chercher les métriques STT
            stt_metrics = {}
            for row in range(1, min(ws_synth.max_row + 1, 20)):
                cell_a = ws_synth.cell(row=row, column=1).value
                cell_b = ws_synth.cell(row=row, column=2).value
                if cell_a and 'STT' in str(cell_a):
                    stt_metrics[cell_a] = cell_b
                    print(f"[INFO] {cell_a}: {cell_b}")
            
            print(f"[OK] {len(stt_metrics)} métriques STT trouvées")
            
            # Test d'ouverture
            wb_result.close()
            
            print("[SUCCESS] Fichier Excel généré et vérifié avec succès!")
            return True
        else:
            print("[ERREUR] Feuille 'Synthèse Globale' non créée")
            return False
            
    except Exception as e:
        print(f"[ERREUR] Test échoué: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False
    finally:
        if 'test_file' in locals() and test_file.exists():
            test_file.unlink()

def main():
    print("=== TEST SIMPLE DE GENERATION ===")
    success = test_simple()
    
    if success:
        print("\n[SUCCESS] Code corrigé et fonctionnel!")
        print("Les fichiers générés par code_export_facturation_STT_V6.py ne devraient plus avoir d'erreur Excel.")
    else:
        print("\n[FAIL] Des corrections supplémentaires sont nécessaires.")

if __name__ == "__main__":
    main()