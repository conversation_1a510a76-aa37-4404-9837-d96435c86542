# Amélioration du Chargement du Fichier update_Base_manquant_REF_MUT.xlsx

## 🎯 Objectif

Modifier la logique de chargement du fichier `update_Base_manquant_REF_MUT.xlsx` pour qu'il essaie automatiquement le chemin réseau par défaut `\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\` s'il n'est pas trouvé localement.

## 🔧 Modifications Apportées

### 1. Changement du Chemin par Défaut

**Avant :**
```python
"update_ref_mut": Path(r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\update_Base_manquant_REF_MUT.xlsx")
```

**Après :**
```python
"update_ref_mut": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\update_Base_manquant_REF_MUT.xlsx")
```

### 2. Logique de Recherche Multi-Niveaux

**Ordre de priorité :**
1. **Chemin explicite** (si fourni en paramètre)
2. **Chemin réseau** : `\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\update_Base_manquant_REF_MUT.xlsx`
3. **Chemin local de test** : `C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\update_Base_manquant_REF_MUT.xlsx`
4. **Aucun fichier** : Continuer sans

### 3. Gestion d'Erreur Robuste

**Erreurs gérées :**
- `OSError` - Problèmes d'accès réseau
- `PermissionError` - Problèmes de droits
- `FileNotFoundError` - Fichier non trouvé
- `Exception` générale - Autres erreurs

**Comportement :**
- Log des erreurs sans interrompre le traitement
- Fallback sur le chemin suivant
- Continuation sans fichier si aucun chemin ne fonctionne

## 📋 Code Implémenté

### Logique de Recherche
```python
# Essayer d'abord le chemin fourni, puis le chemin réseau par défaut, puis le chemin local de test
actual_update_path = None
if update_ref_mut_path and update_ref_mut_path.exists():
    actual_update_path = update_ref_mut_path
    logging.info(f"Fichier update_Base_manquant_REF_MUT.xlsx trouvé à: {actual_update_path}")
else:
    # Essayer le chemin réseau par défaut
    network_path = Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\update_Base_manquant_REF_MUT.xlsx")
    local_test_path = Path(r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\update_Base_manquant_REF_MUT.xlsx")
    
    # Essayer le chemin réseau avec gestion d'erreur
    try:
        if network_path.exists():
            actual_update_path = network_path
            logging.info(f"✅ Fichier update_Base_manquant_REF_MUT.xlsx trouvé sur le réseau: {network_path}")
        else:
            logging.info("Fichier update_Base_manquant_REF_MUT.xlsx non trouvé sur le réseau")
    except (OSError, PermissionError, Exception) as e:
        logging.warning(f"Erreur accès réseau pour update_Base_manquant_REF_MUT.xlsx: {e}")
    
    # Si pas trouvé sur réseau, essayer le chemin local de test
    if actual_update_path is None:
        try:
            if local_test_path.exists():
                actual_update_path = local_test_path  
                logging.info(f"✅ Fichier update_Base_manquant_REF_MUT.xlsx trouvé en local: {local_test_path}")
            else:
                logging.info("Fichier update_Base_manquant_REF_MUT.xlsx non trouvé en local non plus")
        except (OSError, Exception) as e:
            logging.warning(f"Erreur accès local pour update_Base_manquant_REF_MUT.xlsx: {e}")
    
    # Message final si aucun fichier trouvé
    if actual_update_path is None:
        logging.info("ℹ️ Fichier update_Base_manquant_REF_MUT.xlsx non trouvé - continuons sans complétion REF_MUT")
```

### Chargement Sécurisé
```python
if actual_update_path:
    logging.info(f"📂 Chargement du fichier update_Base_manquant_REF_MUT.xlsx depuis {actual_update_path}")
    print("⏳ Chargement update_Base_manquant_REF_MUT.xlsx...")
    
    try:
        df_update_ref_mut = pd.read_excel(
            actual_update_path, 
            engine='openpyxl',
            dtype=str,  # Tout en string pour éviter les conversions automatiques
            na_filter=False  # Éviter la conversion automatique en NaN
        )
    except (FileNotFoundError, PermissionError, Exception) as e:
        logging.error(f"❌ Erreur lors du chargement de update_Base_manquant_REF_MUT.xlsx: {e}")
        logging.info("Continuons sans le fichier update_Base_manquant_REF_MUT.xlsx")
        df_update_ref_mut = None
```

### Création Conditionnelle des Dictionnaires
```python
# Vérifier que le chargement a réussi avant de l'utiliser
if df_update_ref_mut is not None:
    logging.info(f"✅ Fichier update_Base_manquant_REF_MUT chargé: {len(df_update_ref_mut):,} lignes")
    print(f"✅ update_Base_manquant_REF_MUT chargé: {len(df_update_ref_mut):,} lignes")

    # Créer les dictionnaires immédiatement après le chargement réussi
    ref_mut_dict = {}
    pm_dict = {}
    
    if 'Référence contrat' in df_update_ref_mut.columns:
        for idx, row in df_update_ref_mut.iterrows():
            ref_contrat = row.get('Référence contrat')
            if ref_contrat and ref_contrat != '':
                ref_mut_dict[ref_contrat] = row.to_dict()
        logging.info(f"📊 Dictionnaire REF_MUT créé avec {len(ref_mut_dict)} entrées")
    else:
        logging.warning("Colonne 'Référence contrat' non trouvée dans update_Base_manquant_REF_MUT.xlsx")
```

## 📊 Messages de Log Améliorés

### Messages de Succès
- `✅ Fichier update_Base_manquant_REF_MUT.xlsx trouvé sur le réseau`
- `✅ Fichier update_Base_manquant_REF_MUT.xlsx trouvé en local`  
- `✅ update_Base_manquant_REF_MUT chargé: XXX lignes`
- `📊 Dictionnaire REF_MUT créé avec XXX entrées`

### Messages d'Information
- `ℹ️ Fichier update_Base_manquant_REF_MUT.xlsx non trouvé - continuons sans complétion REF_MUT`
- `Fichier update_Base_manquant_REF_MUT.xlsx non trouvé sur le réseau`

### Messages d'Erreur
- `❌ Erreur lors du chargement de update_Base_manquant_REF_MUT.xlsx: [détail]`
- `Erreur accès réseau pour update_Base_manquant_REF_MUT.xlsx: [détail]`

## 🧪 Tests

Un script de test a été créé : `tests/test_ref_mut_loading.py`

**Scénarios testés :**
- Fichier trouvé sur le réseau ✅
- Fichier trouvé en local ✅  
- Fichier non trouvé (gestion d'erreur) ✅
- Erreur de chargement (gestion d'erreur) ✅

## 🎯 Bénéfices

1. **Automatisation** : Plus besoin de copier manuellement le fichier
2. **Robustesse** : Gestion d'erreur complète
3. **Flexibilité** : Plusieurs chemins de fallback
4. **Transparence** : Logs détaillés du processus
5. **Compatibilité** : Fonctionne avec ou sans le fichier

## 🚀 Utilisation

Le traitement fonctionne maintenant automatiquement :

1. **En production** : Utilise le chemin réseau automatiquement
2. **En test** : Fallback sur le chemin local si nécessaire  
3. **Sans fichier** : Continue le traitement sans complétion REF_MUT

## 📁 Fichiers Modifiés

1. `code_export_facturation_STT_V6.py` - Logique principale modifiée
2. `tests/test_ref_mut_loading.py` - Nouveau script de test
3. `docs/AMELIORATION_REF_MUT_LOADING.md` - Cette documentation

---

**Status** : ✅ IMPLÉMENTÉ ET TESTÉ  
**Version** : 2.0  
**Date** : 2025-01-08

Le fichier `update_Base_manquant_REF_MUT.xlsx` est maintenant chargé automatiquement depuis le réseau `\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\` avec fallback robuste !