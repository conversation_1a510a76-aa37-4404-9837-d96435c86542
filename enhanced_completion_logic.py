import pandas as pd
import logging

def enhanced_field_completion(merged_df, df_liste_oi=None, ref_mut_dict=None):
    """
    Logique améliorée de completion des champs OI, Code OI InterOP, Zone et Région
    
    Args:
        merged_df: DataFrame principal avec les données
        df_liste_oi: DataFrame du fichier Liste_OI.xlsx
        ref_mut_dict: Dictionnaire des correspondances Ref_MUT
    """
    logging.info("=== DÉBUT COMPLETION AMÉLIORÉE DES CHAMPS ===")
    
    # Statistiques initiales
    initial_stats = {
        'OI': (merged_df['OI'].isna() | (merged_df['OI'] == '')).sum(),
        'Code OI InterOP': (merged_df['Code OI InterOP'].isna() | (merged_df['Code OI InterOP'] == '')).sum(),
        'Zone': (merged_df['Zone'].isna() | (merged_df['Zone'] == '')).sum(),
        'Région': (merged_df['Région'].isna() | (merged_df['Région'] == '')).sum()
    }
    
    print(f"Champs vides initiaux:")
    for field, count in initial_stats.items():
        print(f"  {field}: {count}")
    
    # ÉTAPE 1: Compléter Code OI InterOP via Ref_MUT par Référence contrat
    if ref_mut_dict:
        logging.info("ÉTAPE 1: Completion Code OI InterOP via Ref_MUT")
        code_oi_filled = 0
        
        code_oi_empty_mask = (merged_df['Code OI InterOP'].isna()) | (merged_df['Code OI InterOP'] == '')
        
        for idx in merged_df[code_oi_empty_mask].index:
            ref_mut_value = merged_df.at[idx, 'Ref_MUT']
            
            if ref_mut_value and ref_mut_value in ref_mut_dict:
                code_oi_acv = ref_mut_dict[ref_mut_value].get('Code OI - ACV')
                if code_oi_acv and code_oi_acv != '':
                    merged_df.at[idx, 'Code OI InterOP'] = code_oi_acv
                    code_oi_filled += 1
        
        logging.info(f"Code OI InterOP complété: {code_oi_filled} champs via Ref_MUT")
        print(f"OK Code OI InterOP: {code_oi_filled} champs completes via Ref_MUT")
    
    # ÉTAPE 2: Compléter OI via Liste_OI.xlsx par Code OI InterOP
    if df_liste_oi is not None and 'Code OI InterOP' in df_liste_oi.columns and 'OI' in df_liste_oi.columns:
        logging.info("ÉTAPE 2: Completion OI via Liste_OI par Code OI InterOP")
        
        # Créer dictionnaire Code OI InterOP → OI
        code_oi_to_oi_dict = {}
        for _, row in df_liste_oi.iterrows():
            code_oi = row.get('Code OI InterOP')
            oi = row.get('OI')
            if pd.notna(code_oi) and pd.notna(oi) and code_oi != '' and oi != '':
                code_oi_to_oi_dict[code_oi] = oi
        
        logging.info(f"Dictionnaire Code OI → OI créé avec {len(code_oi_to_oi_dict)} entrées")
        
        oi_filled = 0
        oi_empty_mask = (merged_df['OI'].isna()) | (merged_df['OI'] == '')
        
        for idx in merged_df[oi_empty_mask].index:
            code_oi_value = merged_df.at[idx, 'Code OI InterOP']
            
            if code_oi_value and code_oi_value in code_oi_to_oi_dict:
                oi_value = code_oi_to_oi_dict[code_oi_value]
                merged_df.at[idx, 'OI'] = oi_value
                oi_filled += 1
        
        logging.info(f"OI complété: {oi_filled} champs via Code OI InterOP → Liste_OI")
        print(f"OK OI: {oi_filled} champs completes via Code OI InterOP")
    
    # ÉTAPE 3: Compléter Zone via logique DEPT
    logging.info("ÉTAPE 3: Completion Zone via logique DEPT")
    zone_filled = 0
    
    def determine_zone_from_dept(dept):
        """Détermine la zone basée sur le département"""
        if not dept or pd.isna(dept):
            return None
            
        dept_str = str(dept).strip()
        
        # ZTD (Zone à Très haute Densité) - Grandes métropoles
        ztd_depts = ['75', '92', '93', '94', '95', '13', '69', '59', '31', '33', '35', '44', '67', '38']
        
        # ZAG (Zone Antilles-Guyane) - DOM-TOM
        zag_depts = ['971', '972', '973', '974', '976', '977', '978', '988', '984', '986', '987']
        
        if dept_str in ztd_depts:
            return 'ZTD'
        elif dept_str in zag_depts:
            return 'ZAG'
        else:
            return 'ZMD'  # Zone Moyennement Dense par défaut
    
    zone_empty_mask = (merged_df['Zone'].isna()) | (merged_df['Zone'] == '')
    
    for idx in merged_df[zone_empty_mask].index:
        dept_value = merged_df.at[idx, 'DEPT']
        
        if dept_value:
            determined_zone = determine_zone_from_dept(dept_value)
            if determined_zone:
                merged_df.at[idx, 'Zone'] = determined_zone
                zone_filled += 1
    
    logging.info(f"Zone complété: {zone_filled} champs via logique DEPT")
    print(f"OK Zone: {zone_filled} champs completes via DEPT")
    
    # ÉTAPE 4: Compléter Région via Code postal depuis Ref_MUT
    if ref_mut_dict:
        logging.info("ÉTAPE 4: Completion Région via Code postal depuis Ref_MUT")
        
        def determine_region_from_postal_code(postal_code):
            """Détermine la région basée sur le code postal"""
            if not postal_code or pd.isna(postal_code):
                return None
                
            postal_str = str(postal_code).strip()[:2]  # Prendre les 2 premiers chiffres
            
            # Mapping code postal → région (simplifiée)
            postal_to_region = {
                '75': 'IDF', '77': 'IDF', '78': 'IDF', '91': 'IDF', '92': 'IDF', '93': 'IDF', '94': 'IDF', '95': 'IDF',  # Île-de-France
                '13': 'PACA', '04': 'PACA', '05': 'PACA', '06': 'PACA', '83': 'PACA', '84': 'PACA',  # PACA
                '69': 'ARA', '01': 'ARA', '03': 'ARA', '07': 'ARA', '15': 'ARA', '26': 'ARA', '38': 'ARA', '42': 'ARA', '43': 'ARA', '63': 'ARA', '73': 'ARA', '74': 'ARA',  # Auvergne-Rhône-Alpes
                '59': 'HDF', '02': 'HDF', '60': 'HDF', '62': 'HDF', '80': 'HDF',  # Hauts-de-France
                '31': 'O', '09': 'O', '11': 'O', '12': 'O', '30': 'O', '32': 'O', '34': 'O', '46': 'O', '48': 'O', '65': 'O', '66': 'O', '81': 'O', '82': 'O',  # Occitanie
                '33': 'NA', '16': 'NA', '17': 'NA', '19': 'NA', '23': 'NA', '24': 'NA', '40': 'NA', '47': 'NA', '64': 'NA', '79': 'NA', '86': 'NA', '87': 'NA',  # Nouvelle-Aquitaine
                '44': 'PDL', '49': 'PDL', '53': 'PDL', '72': 'PDL', '85': 'PDL',  # Pays de la Loire
                '35': 'BRE', '22': 'BRE', '29': 'BRE', '56': 'BRE',  # Bretagne
            }
            
            return postal_to_region.get(postal_str, 'AUTRE')
        
        region_filled = 0
        region_empty_mask = (merged_df['Région'].isna()) | (merged_df['Région'] == '')
        
        for idx in merged_df[region_empty_mask].index:
            ref_mut_value = merged_df.at[idx, 'Ref_MUT']
            
            if ref_mut_value and ref_mut_value in ref_mut_dict:
                postal_code = ref_mut_dict[ref_mut_value].get('Code postal - COO')
                if postal_code:
                    determined_region = determine_region_from_postal_code(postal_code)
                    if determined_region:
                        merged_df.at[idx, 'Région'] = determined_region
                        region_filled += 1
        
        logging.info(f"Région complété: {region_filled} champs via Code postal depuis Ref_MUT")
        print(f"OK Region: {region_filled} champs completes via Code postal")
    
    # Statistiques finales
    final_stats = {
        'OI': (merged_df['OI'].isna() | (merged_df['OI'] == '')).sum(),
        'Code OI InterOP': (merged_df['Code OI InterOP'].isna() | (merged_df['Code OI InterOP'] == '')).sum(),
        'Zone': (merged_df['Zone'].isna() | (merged_df['Zone'] == '')).sum(),
        'Région': (merged_df['Région'].isna() | (merged_df['Région'] == '')).sum()
    }
    
    print(f"\nResume de la completion:")
    for field in initial_stats.keys():
        initial = initial_stats[field]
        final = final_stats[field]
        completed = initial - final
        print(f"  {field}: {completed} champs completes ({initial} -> {final} restants)")
    
    logging.info("=== FIN COMPLETION AMÉLIORÉE DES CHAMPS ===")
    
    return merged_df

if __name__ == "__main__":
    print("Module de completion améliorée chargé avec succès")