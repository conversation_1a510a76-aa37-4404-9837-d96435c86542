import pandas as pd
import os

def analyze_300_extract():
    """Analyse du fichier 300_FTTH_extract3.0.xlsx pour comprendre la structure"""
    
    file_path = r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\300_FTTH_extract3.0.xlsx"
    
    if not os.path.exists(file_path):
        print(f"ERREUR: Fichier non trouve: {file_path}")
        return
    
    print("=== ANALYSE DU FICHIER 300_FTTH_extract3.0.xlsx ===")
    
    try:
        # Lire le fichier Excel
        excel_file = pd.ExcelFile(file_path)
        print(f"Feuilles disponibles: {excel_file.sheet_names}")
        
        # Analyser chaque feuille
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- FEUILLE: {sheet_name} ---")
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"Dimensions: {df.shape}")
            print(f"Colonnes: {list(df.columns)}")
            
            # Identifier les champs de signalisation
            signalization_fields = [
                'Date_resolution_SIG', 'Ex_type', 'Ex_source', 'Ex_famille', 
                'Ex_diagnostic', 'Date_New_exp', 'New_EXP', 'Semaine résol sig', 
                'Mois résol sig', 'Semaine ouv new exp', 'Mois ouv new exp'
            ]
            
            existing_sig_fields = [field for field in signalization_fields if field in df.columns]
            print(f"\nChamps de signalisation trouves: {existing_sig_fields}")
            
            # Analyser les lignes avec des données de signalisation
            if existing_sig_fields:
                sig_mask = df[existing_sig_fields].notna().any(axis=1)
                sig_count = sig_mask.sum()
                print(f"Lignes avec donnees de signalisation: {sig_count}")
                
                if sig_count > 0:
                    print(f"\nExemple de lignes avec signalisation:")
                    sample_df = df[sig_mask].head(3)
                    
                    # Afficher les colonnes principales + signalisation
                    main_cols = ['Identifiant cas', 'Ref OC', 'Type', 'Date résolution', 'Mois_Résolution']
                    display_cols = main_cols + existing_sig_fields
                    available_cols = [col for col in display_cols if col in df.columns]
                    
                    print(sample_df[available_cols].to_string())
                    
                    # Statistiques sur les valeurs non vides
                    print(f"\nStatistiques des champs de signalisation:")
                    for field in existing_sig_fields:
                        non_null_count = df[field].notna().sum()
                        if non_null_count > 0:
                            print(f"  {field}: {non_null_count} valeurs non vides")
                            # Exemples de valeurs
                            sample_values = df[field].dropna().unique()[:5]
                            print(f"    Exemples: {list(sample_values)}")
                            
    except Exception as e:
        print(f"ERREUR lors de l'analyse: {e}")

def identify_transformation_logic():
    """Identifier la logique de transformation signalisation -> expertise"""
    
    file_path = r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\300_FTTH_extract3.0.xlsx"
    
    if not os.path.exists(file_path):
        print(f"ERREUR: Fichier non trouve: {file_path}")
        return
    
    print("\n=== LOGIQUE DE TRANSFORMATION SIGNALISATION -> EXPERTISE ===")
    
    try:
        df = pd.read_excel(file_path)
        
        # Champs principaux
        main_fields = [
            'Identifiant cas', 'Ref OC', 'Type', 'Date résolution', 'Mois_Résolution',
            'Source_OC', 'Famille_OC', 'Diagnostic_OC'
        ]
        
        # Champs de signalisation
        sig_fields = [
            'Date_resolution_SIG', 'Ex_type', 'Ex_source', 'Ex_famille', 'Ex_diagnostic'
        ]
        
        # Trouver les lignes qui ont les deux types de données
        available_main = [col for col in main_fields if col in df.columns]
        available_sig = [col for col in sig_fields if col in df.columns]
        
        if available_sig:
            # Lignes avec données de signalisation
            sig_mask = df[available_sig].notna().any(axis=1)
            transformation_candidates = df[sig_mask]
            
            print(f"Candidats pour transformation: {len(transformation_candidates)} lignes")
            
            if len(transformation_candidates) > 0:
                print(f"\nExemple de transformation necessaire:")
                for idx, row in transformation_candidates.head(2).iterrows():
                    print(f"\n--- LIGNE {idx} ---")
                    print(f"Identifiant: {row.get('Identifiant cas', 'N/A')}")
                    print(f"Ref OC: {row.get('Ref OC', 'N/A')}")
                    
                    # Données expertise (actuelles)
                    print(f"EXPERTISE:")
                    print(f"  Type: {row.get('Type', 'N/A')}")
                    print(f"  Date resolution: {row.get('Date résolution', 'N/A')}")
                    print(f"  Source: {row.get('Source_OC', 'N/A')}")
                    print(f"  Famille: {row.get('Famille_OC', 'N/A')}")
                    print(f"  Diagnostic: {row.get('Diagnostic_OC', 'N/A')}")
                    
                    # Données signalisation (à extraire)
                    print(f"SIGNALISATION:")
                    print(f"  Date resolution SIG: {row.get('Date_resolution_SIG', 'N/A')}")
                    print(f"  Ex_type: {row.get('Ex_type', 'N/A')}")
                    print(f"  Ex_source: {row.get('Ex_source', 'N/A')}")
                    print(f"  Ex_famille: {row.get('Ex_famille', 'N/A')}")
                    print(f"  Ex_diagnostic: {row.get('Ex_diagnostic', 'N/A')}")
        
    except Exception as e:
        print(f"ERREUR lors de l'identification: {e}")

if __name__ == "__main__":
    analyze_300_extract()
    identify_transformation_logic()