#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de génération avec le code principal modifié
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire parent au PATH pour importer le module principal
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_create_summary_sheet():
    """Test de la fonction create_summary_sheet modifiée"""
    try:
        # Importer la fonction modifiée
        from code_export_facturation_STT_V6 import create_summary_sheet
        import pandas as pd
        import openpyxl
        
        # Créer des données de test
        test_data = {
            'Diagnostic_OC': ['STT001', 'STT002', 'AUTRE001', 'STT003', 'AUTRE002'],
            'Facturation éligible': ['OUI', 'NON', 'OUI', 'NON', 'OUI'],
            'Catégorie': ['Cat1', 'Cat1', 'Cat2', 'Cat1', 'Cat2'],
            'OI': ['OI1', 'OI1', 'OI2', 'OI1', 'OI2'],
            'OC': ['OC1', 'OC1', 'OC2', 'OC1', 'OC2']
        }
        
        df_test = pd.DataFrame(test_data)
        
        # Créer un fichier Excel temporaire
        test_file = Path("test_synthese.xlsx")
        
        # Créer un fichier Excel basique
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = 'Rapport'
        
        # Écrire les en-têtes
        for col, header in enumerate(df_test.columns, 1):
            ws.cell(row=1, column=col, value=header)
        
        # Écrire les données
        for row_idx, (_, row) in enumerate(df_test.iterrows(), 2):
            for col_idx, value in enumerate(row, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)
        
        wb.save(test_file)
        
        # Tester la fonction create_summary_sheet
        print("[TEST] Création de la feuille de synthèse...")
        create_summary_sheet(test_file, df_test)
        
        # Vérifier que le fichier s'ouvre sans erreur
        print("[TEST] Vérification de l'ouverture du fichier...")
        wb_test = openpyxl.load_workbook(test_file)
        
        if 'Synthèse Globale' in wb_test.sheetnames:
            print("[OK] Feuille 'Synthèse Globale' créée avec succès")
            
            # Vérifier le contenu
            ws_synth = wb_test['Synthèse Globale']
            print(f"[INFO] Nombre de lignes dans la synthèse: {ws_synth.max_row}")
            print(f"[INFO] Nombre de colonnes dans la synthèse: {ws_synth.max_column}")
            
            # Vérifier quelques cellules clés
            title = ws_synth.cell(row=1, column=1).value
            print(f"[INFO] Titre: {title}")
            
            return True
        else:
            print("[ERREUR] Feuille 'Synthèse Globale' non trouvée")
            return False
            
    except Exception as e:
        print(f"[ERREUR] Test échoué: {str(e)}")
        return False
    finally:
        # Nettoyer le fichier de test
        if test_file.exists():
            test_file.unlink()

def main():
    print("=== TEST DE GENERATION PRINCIPALE ===")
    success = test_create_summary_sheet()
    
    if success:
        print("\n[SUCCESS] Tous les tests réussis!")
        print("Le code principal devrait maintenant générer des fichiers Excel sans erreur.")
    else:
        print("\n[FAIL] Des erreurs persistent dans le code principal.")

if __name__ == "__main__":
    main()