import re
import traceback
import logging
import pandas as pd
from pathlib import Path
from copy import copy
from openpyxl.utils.dataframe import dataframe_to_rows
import datetime
import shutil
import os
import sys
import warnings
import multiprocessing as mp
import numpy as np
import openpyxl
from openpyxl.styles import Font, Alignment
import argparse # for command line arguments
from openpyxl.utils import get_column_letter
from enhanced_completion_logic import enhanced_field_completion
from signalization_duplication_logic import duplicate_signalization_tickets

warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')

def sanitize_filename(name):
    """Nettoie les noms de fichiers des caractères spéciaux"""
    return re.sub(r'[\\/*?:"<>|]', '_', str(name))

def setup_logging(log_file):
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def ensure_directory_exists(directory):
    Path(directory).mkdir(parents=True, exist_ok=True)

def harmonize_stit_name(stit_name):
    """Harmonise les noms de STIT selon les règles définies"""
    if not stit_name or pd.isna(stit_name) or stit_name == '':
        return stit_name
    
    stit_str = str(stit_name).strip().upper()
    
    # Dictionnaire de mapping pour harmoniser les noms STIT
    stit_mapping = {
        # ICART - toutes les variantes
        'ICART': 'ICART',
        'ICART_VTL_PXD': 'ICART',
        'ICART_HZTL_PXD': 'ICART',
        'ICART_FO_BB': 'ICART',
        
        # CIRCET - toutes les variantes
        'CIRCET': 'CIRCET',
        'CIRCET_REMAIN': 'CIRCET',
        'CIRCET_FO_BB': 'CIRCET',
        'CIRCET_VTL_PXD': 'CIRCET',
        'CIRCET _HZTL_PXD': 'CIRCET',  # Attention à l'espace avant _HZTL
        'CIRCET_HZTL_PXD': 'CIRCET',
        
        # AZUR - toutes les variantes
        'AZUR': 'AZUR',
        'AZUR_VERTL': 'AZUR',
        
        # ERT TECHNOLOGIES - toutes les variantes
        'ERT TECHNOLOGIES': 'ERT TECHNOLOGIES',
        'ERT_NRO_TDR': 'ERT TECHNOLOGIES',
        'ERT_VTL_PXD': 'ERT TECHNOLOGIES',
        'ERT_HZTL_PXD': 'ERT TECHNOLOGIES',
        
        # AXECOM - toutes les variantes
        'AXECOM': 'AXECOM',
        'AXECOM_VERTL': 'AXECOM',
        'AXECOM_HZTL': 'AXECOM',
        
        # EOS_TELECOM_FTTH - toutes les variantes
        'EOS_TELECOM_FTTH': 'EOS_TELECOM_FTTH',
        'EOS_VTL_PXD': 'EOS_TELECOM_FTTH',
        'EOS_HZTL_PXD': 'EOS_TELECOM_FTTH',
        
        # CRT_FTTH - toutes les variantes
        'CRT_FTTH': 'CRT_FTTH',
        'CRT_FTTH_VERTL': 'CRT_FTTH',
        'CRT_FTTH_HZTL': 'CRT_FTTH',
        
        # JSC_FRANCE_FTTH
        'JSC_FRANCE_FTTH': 'JSC_FRANCE_FTTH',
        
        # SCOPELEC
        'SCOPELEC': 'SCOPELEC',
        
        # SUDTEL_ANTILLES_FTTH
        'SUDTEL_ANTILLES_FTTH': 'SUDTEL_ANTILLES_FTTH',
        
        # ATS_ZOI_FTTH
        'ATS_ZOI_FTTH': 'ATS_ZOI_FTTH',
        
        # AGT_FTTH - toutes les variantes
        'AGT_FTTH': 'AGT_FTTH',
        'AGT_FTTH_VERTL': 'AGT_FTTH',
        'AGT_FTTH_HZTL': 'AGT_FTTH',
        
        # CONSTRUCTEL_FTTH - toutes les variantes
        'CONSTRUCTEL_FTTH': 'CONSTRUCTEL_FTTH',
        'CONSTRUCTEL_FTTH_VERTL': 'CONSTRUCTEL_FTTH',
        
        # Cas particulier
        'VERN SUR SEICHE DE LA HAL': 'VERN SUR SEICHE DE LA HAL'
    }
    
    # Chercher une correspondance exacte d'abord
    if stit_str in stit_mapping:
        return stit_mapping[stit_str]
    
    # Si pas de correspondance exacte, chercher par préfixe principal
    for variant, standardized in stit_mapping.items():
        if stit_str.startswith(variant.split('_')[0]):
            return standardized
    
    # Si aucune correspondance trouvée, retourner le nom original
    return stit_name

def normalize_region_name(region_name):
    """Normalise les noms de région selon les codes définis"""
    if not region_name or pd.isna(region_name) or region_name == '':
        return region_name
    
    region_str = str(region_name).strip().upper()
    
    # Dictionnaire de mapping pour normaliser les noms de région
    region_mapping = {
        # ILE-DE-FRANCE variants
        'ILE-DE-FRANCE': 'IDF',
        'ÎLE-DE-FRANCE': 'IDF',
        'IDF': 'IDF',
        'PARIS': 'IDF',
        'REGION PARISIENNE': 'IDF',
        
        # MEDITERRANEE variants
        'MEDITERRANEE': 'MED',
        'MÉDITERRANÉE': 'MED',
        'MED': 'MED',
        'PACA': 'MED',
        'PROVENCE-ALPES-COTE D\'AZUR': 'MED',
        'PROVENCE-ALPES-CÔTE D\'AZUR': 'MED',
        'CORSE': 'MED',
        
        # CENTRE-EST variants
        'CENTRE-EST': 'CE',
        'CE': 'CE',
        'AUVERGNE-RHONE-ALPES': 'CE',
        'AUVERGNE-RHÔNE-ALPES': 'CE',
        'RHONE-ALPES': 'CE',
        'RHÔNE-ALPES': 'CE',
        'BOURGOGNE-FRANCHE-COMTE': 'CE',
        'BOURGOGNE-FRANCHE-COMTÉ': 'CE',
        
        # SUD-OUEST variants
        'SUD-OUEST': 'SO',
        'SO': 'SO',
        'NOUVELLE-AQUITAINE': 'SO',
        'OCCITANIE': 'SO',
        'AQUITAINE': 'SO',
        'MIDI-PYRENEES': 'SO',
        'MIDI-PYRÉNÉES': 'SO',
        'LANGUEDOC-ROUSSILLON': 'SO',
        
        # NORD-EST variants
        'NORD-EST': 'NE',
        'NE': 'NE',
        'GRAND-EST': 'NE',
        'GRAND EST': 'NE',
        'ALSACE': 'NE',
        'LORRAINE': 'NE',
        'CHAMPAGNE-ARDENNE': 'NE',
        'HAUTS-DE-FRANCE': 'NE',
        'NORD-PAS-DE-CALAIS': 'NE',
        'PICARDIE': 'NE',
        
        # OUEST variants
        'OUEST': 'O',
        'O': 'O',
        'BRETAGNE': 'O',
        'PAYS DE LA LOIRE': 'O',
        'PAYS-DE-LA-LOIRE': 'O',
        'CENTRE-VAL DE LOIRE': 'O',
        'CENTRE': 'O',
        'NORMANDIE': 'O',
        'BASSE-NORMANDIE': 'O',
        'HAUTE-NORMANDIE': 'O',
        
        # ZAG (Zone Antilles-Guyane) variants
        'ZAG': 'ZAG',
        'ANTILLES-GUYANE': 'ZAG',
        'ANTILLES': 'ZAG',
        'GUYANE': 'ZAG',
        'MARTINIQUE': 'ZAG',
        'GUADELOUPE': 'ZAG',
        'REUNION': 'ZAG',
        'RÉUNION': 'ZAG',
        'MAYOTTE': 'ZAG',
        
        # SE (Sud Est) variants
        'SUD EST': 'SE',
        'SUD-EST': 'SE',
        'SE': 'SE',
        
        # SRR (Sud-Région-Réseau) variants
        'SRR': 'SRR',
        'SUD-REGION-RESEAU': 'SRR',
        'SUD-RÉGION-RÉSEAU': 'SRR'
    }
    
    # Chercher une correspondance exacte d'abord
    if region_str in region_mapping:
        return region_mapping[region_str]
    
    # Si pas de correspondance exacte, chercher par mots-clés
    for region_variant, standardized in region_mapping.items():
        if region_variant in region_str or region_str in region_variant:
            return standardized
    
    # Si aucune correspondance trouvée, retourner le nom original
    return region_name

def determine_zone_from_dept(dept_code):
    """Détermine la zone ZTD/ZMD basée sur le code département"""
    if not dept_code or pd.isna(dept_code) or dept_code == '':
        return None
    
    dept_str = str(dept_code).strip()
    
    # Départements ZTD (Zone à Très haute Densité) - Généralement les grandes métropoles
    ztd_departments = {
        # Île-de-France (forte densité urbaine)
        '75', '92', '93', '94',  # Paris et petite couronne
        '77', '78', '91', '95',  # Grande couronne IDF
        
        # Autres grandes métropoles
        '13',  # Bouches-du-Rhône (Marseille)
        '69',  # Rhône (Lyon)
        '59',  # Nord (Lille métropole)
        '31',  # Haute-Garonne (Toulouse)
        '33',  # Gironde (Bordeaux)
        '44',  # Loire-Atlantique (Nantes)
        '67',  # Bas-Rhin (Strasbourg)
        '35',  # Ille-et-Vilaine (Rennes)
        '34',  # Hérault (Montpellier)
        '06',  # Alpes-Maritimes (Nice)
        '38',  # Isère (Grenoble)
        '62',  # Pas-de-Calais (zones urbaines)
        '76',  # Seine-Maritime (Rouen/Le Havre)
        '54',  # Meurthe-et-Moselle (Nancy)
        '57',  # Moselle (Metz)
        '68',  # Haut-Rhin (Mulhouse)
        '74',  # Haute-Savoie (Annecy)
        '64'   # Pyrénées-Atlantiques (Pau/Bayonne)
    }
    
    # Tous les autres départements sont considérés comme ZMD (Zone Moyennement Dense)
    if dept_str in ztd_departments:
        return 'ZTD'
    else:
        return 'ZMD'

def determine_region_from_dept(dept_code):
    """Détermine la région basée sur le code département"""
    if not dept_code or pd.isna(dept_code) or dept_code == '':
        return None
    
    dept_str = str(dept_code).strip()
    
    # Mapping départements → régions selon votre nomenclature
    dept_to_region = {
        # IDF - Île-de-France
        '75': 'IDF', '77': 'IDF', '78': 'IDF', '91': 'IDF', 
        '92': 'IDF', '93': 'IDF', '94': 'IDF', '95': 'IDF',
        
        # MED - Méditerranée (PACA + Corse)
        '04': 'MED', '05': 'MED', '06': 'MED', '13': 'MED', 
        '83': 'MED', '84': 'MED', '2A': 'MED', '2B': 'MED',
        
        # CE - Centre-Est (Auvergne-Rhône-Alpes + Bourgogne-Franche-Comté)
        '01': 'CE', '03': 'CE', '07': 'CE', '15': 'CE', '21': 'CE', 
        '25': 'CE', '26': 'CE', '38': 'CE', '39': 'CE', '42': 'CE', 
        '43': 'CE', '58': 'CE', '63': 'CE', '69': 'CE', '70': 'CE', 
        '71': 'CE', '73': 'CE', '74': 'CE', '89': 'CE', '90': 'CE',
        
        # SO - Sud-Ouest (Nouvelle-Aquitaine + Occitanie)
        '09': 'SO', '11': 'SO', '12': 'SO', '16': 'SO', '17': 'SO', 
        '19': 'SO', '23': 'SO', '24': 'SO', '30': 'SO', '31': 'SO', 
        '32': 'SO', '33': 'SO', '34': 'SO', '40': 'SO', '46': 'SO', 
        '47': 'SO', '48': 'SO', '64': 'SO', '65': 'SO', '66': 'SO', 
        '79': 'SO', '81': 'SO', '82': 'SO', '86': 'SO', '87': 'SO',
        
        # NE - Nord-Est (Grand-Est + Hauts-de-France)
        '02': 'NE', '08': 'NE', '10': 'NE', '51': 'NE', '52': 'NE', 
        '54': 'NE', '55': 'NE', '57': 'NE', '59': 'NE', '60': 'NE', 
        '62': 'NE', '67': 'NE', '68': 'NE', '80': 'NE', '88': 'NE',
        
        # O - Ouest (Bretagne + Pays de la Loire + Normandie + Centre-Val de Loire)
        '14': 'O', '18': 'O', '22': 'O', '27': 'O', '28': 'O', 
        '29': 'O', '35': 'O', '36': 'O', '37': 'O', '41': 'O', 
        '44': 'O', '45': 'O', '49': 'O', '50': 'O', '53': 'O', 
        '56': 'O', '61': 'O', '72': 'O', '76': 'O', '85': 'O',
        
        # ZAG - Zone Antilles-Guyane (DOM-TOM)
        '971': 'ZAG', '972': 'ZAG', '973': 'ZAG', '974': 'ZAG', 
        '975': 'ZAG', '976': 'ZAG', '986': 'ZAG', '987': 'ZAG', '988': 'ZAG',
        
        # SRR - Sud-Région-Réseau (à définir selon vos besoins spécifiques)
        # Ajoutez ici les départements qui correspondent à SRR selon votre logique métier
    }
    
    return dept_to_region.get(dept_str, None)

def is_file_today(file_path):
    """Vérifie si le fichier existe et a été modifié aujourd'hui."""
    return os.path.exists(file_path) and datetime.date.fromtimestamp(os.path.getmtime(file_path)).date() == datetime.date.today()

def copy_file(source, destination):
    try:
        # Vérifier si le fichier source et le fichier destination sont identiques
        if os.path.exists(destination) and abs(os.path.getmtime(source) - os.path.getmtime(destination)) < 1:  # Ajout comparaison date de modif
             logging.info(f"Fichier déjà à jour : {os.path.basename(source)}")  # Si les fichiers sont à la même date, on ne recopie pas
             return True
        shutil.copy2(source, destination)
        logging.info(f"Fichier copié : {os.path.basename(source)}")
        return True
    except Exception as e:
        logging.error(f"Erreur copie {source} : {str(e)}")
        return False

def sanitize_excel_output(df):
    """Nettoie et formate les données pour l'export Excel en préservant la structure exacte."""
    logging.info("=== NETTOYAGE POUR EXPORT EXCEL ===")
    logging.info(f"Colonnes avant nettoyage: {list(df.columns)}")
    
    # Fix column name case issues and duplicates
    if 'statut' in df.columns and 'Statut' in df.columns:
        # Remove duplicate 'Statut' column (keep the first occurrence)
        df = df.loc[:, ~df.columns.duplicated()]
        logging.info("Duplicate 'Statut' column removed")
    
    # Rename 'statut' to 'Statut' if needed
    if 'statut' in df.columns and 'Statut' not in df.columns:
        df = df.rename(columns={'statut': 'Statut'})
        logging.info("Column 'statut' renamed to 'Statut'")
    
    # Add missing columns with default values if they don't exist
    missing_columns = {
        'Délai_résolution': '',
        'Délai_résolution_jours': '',
        'Catégorie': '',
        'Facturation éligible': ''
    }
    
    for col, default_value in missing_columns.items():
        if col not in df.columns:
            df[col] = default_value
            logging.info(f"Added missing column '{col}' with default value")
    
    # Ensure proper column order
    expected_columns = [
        'Statut', 'Identifiant cas', 'Ref OC', 'OC', 'Ref_MUT', 'Prise', 
        'Diagnostic en cours', 'Date ouverture du cas', 'Date résolution', 
        'Mois_Création', 'Mois_Résolution', 'Type', 'Source_OC', 'Famille_OC', 
        'Diagnostic_OC', 'Commentaire Résolution', 'TT STIT', 'PM', 
        'Région', 'STIT', 'DEPT', 'Propriétaire', 'Zone', 'Code OI InterOP', 
        'OI', 'Délai_résolution', 'Délai_résolution_jours', 'Catégorie', 
        'Facturation éligible'
    ]
    
    # Reorder columns to match expected structure
    existing_columns = [col for col in expected_columns if col in df.columns]
    df = df[existing_columns]
    logging.info("Columns reordered to match expected structure")
    
    # Formater les colonnes de date au format JJ/MM/AAAA HH:MM:SS
    date_columns = ['Date ouverture du cas', 'Date résolution']
    for col in date_columns:
        if col in df.columns:
            try:
                # Vérifier si la colonne est de type datetime
                if pd.api.types.is_datetime64_any_dtype(df[col]):
                    # Compter les valeurs non-NaT
                    non_nat_count = (~df[col].isna()).sum()
                    logging.info(f"Colonne {col}: {non_nat_count} valeurs non-NaT sur {len(df)} lignes")

                    # Convertir les dates au format français JJ/MM/AAAA HH:MM:SS
                    df[col] = df[col].dt.strftime('%d/%m/%Y %H:%M:%S')
                    logging.info(f"Colonne {col} formatée au format JJ/MM/AAAA HH:MM:SS")
                else:
                    # Si la colonne n'est pas de type datetime, essayer de la convertir
                    logging.warning(f"Colonne {col} n'est pas de type datetime, tentative de conversion")
                    try:
                        # Sauvegarder la colonne originale
                        original_col = df[col].copy()

                        # Essayer de convertir en datetime
                        df[col] = pd.to_datetime(df[col], errors='coerce', dayfirst=True)

                        # Vérifier si la conversion a réussi
                        if df[col].isna().sum() > len(df) * 0.5:  # Si plus de 50% sont NaT
                            # Restaurer la colonne originale
                            df[col] = original_col
                            logging.warning(f"Conversion de {col} en datetime a échoué, trop de valeurs NaT")
                        else:
                            # Formater les dates
                            df[col] = df[col].dt.strftime('%d/%m/%Y %H:%M:%S')
                            logging.info(f"Colonne {col} convertie et formatée au format JJ/MM/AAAA HH:MM:SS")
                    except Exception as e:
                        # Restaurer la colonne originale en cas d'erreur
                        df[col] = original_col
                        logging.warning(f"Erreur lors de la conversion de {col}: {str(e)}")
            except Exception as e:
                logging.warning(f"Impossible de formater la colonne '{col}': {str(e)}")

    # Traitement spécial pour DEPT - s'assurer qu'il est en format texte sans décimales
    if 'DEPT' in df.columns:
        try:
            # Convertir en string et supprimer les décimales (.0)
            df['DEPT'] = df['DEPT'].astype(str).replace(r'\.0$', '', regex=True)
            logging.info("Colonne DEPT formatée en texte sans décimales")
        except Exception as e:
            logging.warning(f"Erreur lors du formatage de la colonne DEPT: {str(e)}")

    # Traitement spécial pour Délai_résolution - remplacer les points par des virgules
    if 'Délai_résolution' in df.columns:
        try:
            # Convertir en string et remplacer les points par des virgules
            df['Délai_résolution'] = df['Délai_résolution'].astype(str).replace('.', ',')
            logging.info("Colonne Délai_résolution formatée avec virgules au lieu de points")
        except Exception as e:
            logging.warning(f"Erreur lors du formatage de la colonne Délai_résolution: {str(e)}")

    # Nettoyer les autres colonnes
    for col in df.columns:
        try:
            if col not in date_columns or not pd.api.types.is_datetime64_any_dtype(df[col]):
                df[col] = df[col].astype(str).str.replace("=", "'=", regex=False)
                df[col] = df[col].str.strip()
        except (AttributeError, TypeError):
            logging.warning(f"Impossible de nettoyer la colonne '{col}'. Le type de données n'est pas compatible.")
            continue
    
    # Vérification finale de la structure
    expected_columns = [
        'Statut', 'Identifiant cas', 'Ref OC', 'OC', 'Ref_MUT', 'Prise', 
        'Diagnostic en cours', 'Date ouverture du cas', 'Date résolution', 
        'Mois_Création', 'Mois_Résolution', 'Type', 'Source_OC', 'Famille_OC', 
        'Diagnostic_OC', 'Commentaire Résolution', 'TT STIT', 'PM', 
        'Région', 'STIT', 'DEPT', 'Propriétaire', 'Zone', 'Code OI InterOP', 
        'OI', 'Délai_résolution', 'Délai_résolution_jours', 'Catégorie', 
        'Facturation éligible'
    ]
    
    if list(df.columns) == expected_columns:
        logging.info("[OK] Structure finale CONFORME pour l'export Excel")
    else:
        logging.warning("[ATTENTION] Structure finale différente de celle attendue!")
        logging.warning(f"Attendue: {expected_columns}")
        logging.warning(f"Actuelle: {list(df.columns)}")
    
    logging.info(f"DataFrame final prêt pour export: {len(df)} lignes x {len(df.columns)} colonnes")
    logging.info("=== FIN NETTOYAGE EXCEL ===")
    
    return df

def merge_datasets(extract_path, mut_path, stit_path=None, analyse_manquant_path=None, pm_sadira_path=None, liste_oi_path=None, update_ref_mut_path=None):
    try:
        logging.info("=== NOUVELLE LOGIQUE DE FUSION ===")
        
        # CHARGEMENT OPTIMISÉ AVEC SUIVI DE PROGRESSION
        logging.info("Chargement du fichier reporting (STT/RET)...")
        print("Chargement FTTH - Reporting cas STC_REFMUT.xlsx...")
        
        # Chargement optimisé du fichier reporting
        df_reporting = pd.read_excel(
            mut_path, 
            engine='openpyxl',
            # Optimisations pour les gros fichiers
            dtype=str,  # Tout en string pour éviter les conversions automatiques
            na_filter=False  # Éviter la conversion automatique en NaN
        )
        
        logging.info(f"Fichier reporting charge: {len(df_reporting):,} lignes")
        print(f"Reporting charge: {len(df_reporting):,} lignes")
        
        logging.info("Chargement du fichier extract (enrichissement)...")
        print("Chargement 300_FTTH_extract3.0.xlsx...")
        
        # Chargement optimisé du fichier extract
        df_extract = pd.read_excel(
            extract_path, 
            engine='openpyxl',
            dtype=str,  # Tout en string pour éviter les conversions automatiques
            na_filter=False  # Éviter la conversion automatique en NaN
        )
        
        logging.info(f"Fichier extract charge: {len(df_extract):,} lignes")
        print(f"Extract charge: {len(df_extract):,} lignes")
        
        # Chargement du fichier STIT si fourni
        df_stit = None
        if stit_path and stit_path.exists():
            logging.info("Chargement du fichier _FTTH_TT_STIT.xlsx...")
            print("Chargement _FTTH_TT_STIT.xlsx...")
            
            df_stit = pd.read_excel(
                stit_path, 
                engine='openpyxl',
                dtype=str,  # Tout en string pour éviter les conversions automatiques
                na_filter=False  # Éviter la conversion automatique en NaN
            )
            
            logging.info(f"Fichier STIT charge: {len(df_stit):,} lignes")
            print(f"STIT charge: {len(df_stit):,} lignes")
        else:
            logging.warning("Fichier _FTTH_TT_STIT.xlsx non trouvé ou non spécifié")
        
        # Chargement du fichier Analyse_STIT_manquant si fourni
        df_analyse_manquant = None
        if analyse_manquant_path and analyse_manquant_path.exists():
            logging.info("Chargement du fichier Analyse_STIT_manquant.xlsx...")
            print("Chargement Analyse_STIT_manquant.xlsx...")
            
            df_analyse_manquant = pd.read_excel(
                analyse_manquant_path, 
                engine='openpyxl',
                dtype=str,  # Tout en string pour éviter les conversions automatiques
                na_filter=False  # Éviter la conversion automatique en NaN
            )
            
            logging.info(f"Fichier Analyse_STIT_manquant charge: {len(df_analyse_manquant):,} lignes")
            print(f"Analyse_STIT_manquant charge: {len(df_analyse_manquant):,} lignes")
        else:
            logging.warning("Fichier Analyse_STIT_manquant.xlsx non trouvé ou non spécifié")
        
        # Chargement du fichier FTTH_Liste_PM_source_sadira si fourni
        df_pm_sadira = None
        if pm_sadira_path and pm_sadira_path.exists():
            logging.info("Chargement du fichier FTTH_Liste_PM_source_sadira.xlsx...")
            print("Chargement FTTH_Liste_PM_source_sadira.xlsx...")
            
            df_pm_sadira = pd.read_excel(
                pm_sadira_path, 
                engine='openpyxl',
                dtype=str,  # Tout en string pour éviter les conversions automatiques
                na_filter=False  # Éviter la conversion automatique en NaN
            )
            
            logging.info(f"Fichier FTTH_Liste_PM_source_sadira charge: {len(df_pm_sadira):,} lignes")
            print(f"FTTH_Liste_PM_source_sadira charge: {len(df_pm_sadira):,} lignes")
        else:
            logging.warning("Fichier FTTH_Liste_PM_source_sadira.xlsx non trouvé ou non spécifié")
        
        # Chargement du fichier Liste_OI si fourni
        df_liste_oi = None
        if liste_oi_path and liste_oi_path.exists():
            logging.info("Chargement du fichier Liste_OI.xlsx...")
            print("Chargement Liste_OI.xlsx...")
            
            df_liste_oi = pd.read_excel(
                liste_oi_path, 
                engine='openpyxl',
                dtype=str,  # Tout en string pour éviter les conversions automatiques
                na_filter=False  # Éviter la conversion automatique en NaN
            )
            
            logging.info(f"Fichier Liste_OI charge: {len(df_liste_oi):,} lignes")
            print(f"Liste_OI charge: {len(df_liste_oi):,} lignes")
        else:
            logging.warning("Fichier Liste_OI.xlsx non trouvé ou non spécifié")
        
        # Chargement du fichier update_Base_manquant_REF_MUT si fourni
        df_update_ref_mut = None
        ref_mut_dict = None  # Initialiser ici pour éviter les erreurs
        pm_dict = None      # Initialiser ici pour éviter les erreurs
        
        # Essayer d'abord le chemin fourni, puis le chemin réseau par défaut, puis le chemin local de test
        actual_update_path = None
        if update_ref_mut_path and update_ref_mut_path.exists():
            actual_update_path = update_ref_mut_path
            logging.info(f"Fichier update_Base_manquant_REF_MUT.xlsx trouvé à: {actual_update_path}")
        else:
            # Essayer le chemin réseau par défaut
            network_path = Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\update_Base_manquant_REF_MUT.xlsx")
            local_test_path = Path(r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_05\update_Base_manquant_REF_MUT.xlsx")
            
            # Essayer le chemin réseau avec gestion d'erreur
            try:
                if network_path.exists():
                    actual_update_path = network_path
                    logging.info(f"✅ Fichier update_Base_manquant_REF_MUT.xlsx trouvé sur le réseau: {network_path}")
                else:
                    logging.info("Fichier update_Base_manquant_REF_MUT.xlsx non trouvé sur le réseau")
            except (OSError, PermissionError, Exception) as e:
                logging.warning(f"Erreur accès réseau pour update_Base_manquant_REF_MUT.xlsx: {e}")
            
            # Si pas trouvé sur réseau, essayer le chemin local de test
            if actual_update_path is None:
                try:
                    if local_test_path.exists():
                        actual_update_path = local_test_path  
                        logging.info(f"✅ Fichier update_Base_manquant_REF_MUT.xlsx trouvé en local: {local_test_path}")
                    else:
                        logging.info("Fichier update_Base_manquant_REF_MUT.xlsx non trouvé en local non plus")
                except (OSError, Exception) as e:
                    logging.warning(f"Erreur accès local pour update_Base_manquant_REF_MUT.xlsx: {e}")
            
            # Message final si aucun fichier trouvé
            if actual_update_path is None:
                logging.info("ℹ️ Fichier update_Base_manquant_REF_MUT.xlsx non trouvé - continuons sans complétion REF_MUT")
        
        if actual_update_path:
            logging.info(f"📂 Chargement du fichier update_Base_manquant_REF_MUT.xlsx depuis {actual_update_path}")
            print("⏳ Chargement update_Base_manquant_REF_MUT.xlsx...")
            
            try:
                df_update_ref_mut = pd.read_excel(
                    actual_update_path, 
                    engine='openpyxl',
                    dtype=str,  # Tout en string pour éviter les conversions automatiques
                    na_filter=False  # Éviter la conversion automatique en NaN
                )
            except (FileNotFoundError, PermissionError, Exception) as e:
                logging.error(f"❌ Erreur lors du chargement de update_Base_manquant_REF_MUT.xlsx: {e}")
                logging.info("Continuons sans le fichier update_Base_manquant_REF_MUT.xlsx")
                df_update_ref_mut = None
            
            # Vérifier que le chargement a réussi avant de l'utiliser
            if df_update_ref_mut is not None:
                logging.info(f"✅ Fichier update_Base_manquant_REF_MUT chargé: {len(df_update_ref_mut):,} lignes")
                print(f"✅ update_Base_manquant_REF_MUT chargé: {len(df_update_ref_mut):,} lignes")
            
                # Créer les dictionnaires immédiatement après le chargement réussi
                ref_mut_dict = {}
                pm_dict = {}
                
                if 'Référence contrat' in df_update_ref_mut.columns:
                    for idx, row in df_update_ref_mut.iterrows():
                        ref_contrat = row.get('Référence contrat')
                        if ref_contrat and ref_contrat != '':
                            ref_mut_dict[ref_contrat] = row.to_dict()
                    logging.info(f"📊 Dictionnaire REF_MUT créé avec {len(ref_mut_dict)} entrées")
                else:
                    logging.warning("Colonne 'Référence contrat' non trouvée dans update_Base_manquant_REF_MUT.xlsx")
                
                if 'Sro' in df_update_ref_mut.columns:
                    for idx, row in df_update_ref_mut.iterrows():
                        sro = row.get('Sro')
                        if sro and sro != '':
                            pm_dict[sro] = row.to_dict()
                    logging.info(f"📊 Dictionnaire PM créé avec {len(pm_dict)} entrées")
                else:
                    logging.warning("Colonne 'Sro' non trouvée dans update_Base_manquant_REF_MUT.xlsx")
            else:
                logging.info("❌ Échec du chargement de update_Base_manquant_REF_MUT.xlsx")
        else:
            logging.info("ℹ️ Fichier update_Base_manquant_REF_MUT.xlsx non trouvé - continuons sans complétion REF_MUT")
        
        # Nettoyage des noms de colonnes
        print("Nettoyage des colonnes...")
        dataframes_to_clean = [df_extract, df_reporting]
        if df_stit is not None:
            dataframes_to_clean.append(df_stit)
        if df_analyse_manquant is not None:
            dataframes_to_clean.append(df_analyse_manquant)
        if df_pm_sadira is not None:
            dataframes_to_clean.append(df_pm_sadira)
        if df_liste_oi is not None:
            dataframes_to_clean.append(df_liste_oi)
        if df_update_ref_mut is not None:
            dataframes_to_clean.append(df_update_ref_mut)
            
        for df in dataframes_to_clean:
            df.columns = df.columns.astype(str).str.strip()
        
        logging.info("APPROCHE INVERSEE:")
        logging.info("1. Base = FTTH - Reporting cas STC_REFMUT.xlsx (STT/RET uniquement)")
        logging.info("2. Enrichissement = 300_FTTH_extract3.0.xlsx via Identifiant cas")
        if df_stit is not None:
            logging.info("3. Enrichissement supplémentaire = _FTTH_TT_STIT.xlsx via TT STIT")
        if df_analyse_manquant is not None:
            logging.info("4. Enrichissement final = Analyse_STIT_manquant.xlsx via TT STIT")
        if df_pm_sadira is not None:
            logging.info("5. Enrichissement PM/Propriétaire = FTTH_Liste_PM_source_sadira.xlsx via PM")
        if df_liste_oi is not None:
            logging.info("6. Enrichissement OI = Liste_OI.xlsx via Propriétaire - LTDET")
        if df_update_ref_mut is not None:
            logging.info("7. Enrichissement REF_MUT = update_Base_manquant_REF_MUT.xlsx via Ref_MUT et PM")
        
        # ANALYSE DES FICHIERS
        logging.info("=== ANALYSE DES FICHIERS ===")
        logging.info(f"300_FTTH_extract3.0.xlsx: {len(df_extract)} lignes, colonnes: {list(df_extract.columns)}")
        logging.info(f"FTTH - Reporting cas STC_REFMUT.xlsx: {len(df_reporting)} lignes, colonnes: {list(df_reporting.columns)}")
        if df_stit is not None:
            logging.info(f"_FTTH_TT_STIT.xlsx: {len(df_stit)} lignes, colonnes: {list(df_stit.columns)}")
        if df_analyse_manquant is not None:
            logging.info(f"Analyse_STIT_manquant.xlsx: {len(df_analyse_manquant)} lignes, colonnes: {list(df_analyse_manquant.columns)}")
        if df_pm_sadira is not None:
            logging.info(f"FTTH_Liste_PM_source_sadira.xlsx: {len(df_pm_sadira)} lignes, colonnes: {list(df_pm_sadira.columns)}")
        if df_liste_oi is not None:
            logging.info(f"Liste_OI.xlsx: {len(df_liste_oi)} lignes, colonnes: {list(df_liste_oi.columns)}")
        if df_update_ref_mut is not None:
            logging.info(f"update_Base_manquant_REF_MUT.xlsx: {len(df_update_ref_mut)} lignes, colonnes: {list(df_update_ref_mut.columns)}")
        
        # Vérifier les clés de jointure - NOUVELLE LOGIQUE
        identifiant_cas_in_reporting = 'Identifiant cas' in df_reporting.columns
        identifiant_cas_in_extract = 'Identifiant cas' in df_extract.columns
        
        logging.info(f"CLE DE JOINTURE 'Identifiant cas':")
        logging.info(f"   Dans reporting: {identifiant_cas_in_reporting}")
        logging.info(f"   Dans extract: {identifiant_cas_in_extract}")
        
        if identifiant_cas_in_reporting:
            id_cas_count_reporting = df_reporting['Identifiant cas'].notna().sum()
            logging.info(f"   Reporting: {id_cas_count_reporting}/{len(df_reporting)} non nulles ({id_cas_count_reporting/len(df_reporting)*100:.1f}%)")
            
            # Exemples d'Identifiant cas dans le reporting
            sample_ids_reporting = df_reporting['Identifiant cas'].dropna().head(5).tolist()
            logging.info(f"   Exemples dans reporting: {sample_ids_reporting}")
        
        if identifiant_cas_in_extract:
            id_cas_count_extract = df_extract['Identifiant cas'].notna().sum()
            logging.info(f"   Extract: {id_cas_count_extract}/{len(df_extract)} non nulles ({id_cas_count_extract/len(df_extract)*100:.1f}%)")
            
            # Exemples d'Identifiant cas dans l'extract
            sample_ids_extract = df_extract['Identifiant cas'].dropna().head(5).tolist()
            logging.info(f"   Exemples dans extract: {sample_ids_extract}")
        
        # Vérifier la cohérence des Identifiant cas entre les deux fichiers
        if identifiant_cas_in_reporting and identifiant_cas_in_extract:
            common_ids = set(df_reporting['Identifiant cas'].dropna()) & set(df_extract['Identifiant cas'].dropna())
            logging.info(f"[JOINTURE] Identifiants communs: {len(common_ids)} sur {len(set(df_reporting['Identifiant cas'].dropna()))} du reporting")
            
            if len(common_ids) > 0:
                logging.info(f"[OK] Jointure possible sur {len(common_ids)} cas communs")
                logging.info(f"   Exemples d'IDs communs: {list(common_ids)[:5]}")
            else:
                logging.warning("[ERREUR] AUCUN Identifiant cas commun trouvé!")
        
        logging.info("=== FIN ANALYSE ===")

        # NOUVELLE LOGIQUE DE FUSION : REPORTING EN BASE
        if not identifiant_cas_in_reporting or not identifiant_cas_in_extract:
            raise ValueError("[ERREUR] Colonne 'Identifiant cas' manquante dans un des fichiers!")
        
        # Identifier les colonnes utiles dans df_extract pour l'enrichissement
        logging.info("=== PRÉPARATION DE L'ENRICHISSEMENT ===")
        
        # Colonnes à récupérer du fichier extract (mappées vers les noms finaux)
        extract_columns_mapping = {
            'Identifiant cas': 'Identifiant cas',  # Clé de jointure
            'A pour cas parent': 'TT STIT',  # Utilisation prioritaire de "A pour cas parent" pour TT STIT
            'TT STIT': 'TT STIT',
            'Elément impacté': 'PM',
            'Sous-traitant intervention': 'STIT', 
            'Region': 'Région',
            'Ref OC': 'Ref OC',
            'Prise': 'Prise',
            'Source_OC': 'Source_OC',
            'Famille_OC': 'Famille_OC',
            'DEPT': 'DEPT',
            'Propriétaire - LTDET': 'Propriétaire',  # Utilisation de "Propriétaire - LTDET" pour Propriétaire
            'Propriétaire': 'Propriétaire',
            'Zone': 'Zone',
            'Code OI InterOP': 'Code OI InterOP',
            'OI': 'OI'
        }
        
        # Vérifier quelles colonnes sont disponibles dans l'extract
        available_extract_columns = []
        extract_mapping_final = {}
        
        for extract_col, final_col in extract_columns_mapping.items():
            if extract_col in df_extract.columns:
                available_extract_columns.append(extract_col)
                extract_mapping_final[extract_col] = final_col
                logging.info(f"[OK] Colonne '{extract_col}' -> '{final_col}' disponible")
            else:
                logging.warning(f"✗ Colonne '{extract_col}' non trouvée dans extract")
        
        logging.info(f"[COLONNES] Colonnes à récupérer de l'extract: {available_extract_columns}")
        
        # Effectuer la jointure : REPORTING (base) enrichi par EXTRACT
        logging.info("=== FUSION DES DONNÉES ===")
        print("⏳ Fusion des données en cours...")
        
        # Optimisation : vérifier la taille des données avant jointure
        reporting_size = len(df_reporting)
        extract_size = len(df_extract)
        logging.info(f"[FUSION] Fusion: {reporting_size:,} cas (base) × {extract_size:,} lignes (enrichissement)")
        
        # PRÉPARER L'EXTRACT AVANT LA JOINTURE pour éviter les doublons
        logging.info("=== PRÉPARATION AVANT JOINTURE ===")
        
        # Créer un DataFrame extract prêt pour la jointure
        df_extract_prepared = df_extract[available_extract_columns].copy()
        
        # Renommer AVANT la jointure selon le mapping défini
        if extract_mapping_final:
            rename_dict = {k: v for k, v in extract_mapping_final.items() if k != v}
            if rename_dict:
                df_extract_prepared.rename(columns=rename_dict, inplace=True)
                logging.info(f"Colonnes de l'extract renommées AVANT jointure: {rename_dict}")
        
        # Identifier les colonnes communes (sauf 'Identifiant cas')
        reporting_cols = set(df_reporting.columns)
        extract_cols = set(df_extract_prepared.columns)
        common_cols = reporting_cols & extract_cols
        common_cols.discard('Identifiant cas')  # Garder la clé de jointure
        
        if common_cols:
            logging.info(f"Colonnes communes détectées: {list(common_cols)}")
            # Renommer les colonnes communes dans l'extract pour éviter les conflits
            for col in common_cols:
                new_name = f"{col}_from_extract"
                df_extract_prepared.rename(columns={col: new_name}, inplace=True)
                logging.info(f"Colonne '{col}' renommée en '{new_name}' dans l'extract")
        
        # Jointure optimisée
        merged_df = pd.merge(
            df_reporting,  # BASE : FTTH - Reporting cas STC_REFMUT.xlsx
            df_extract_prepared,  # ENRICHISSEMENT : 300_FTTH_extract3.0.xlsx préparé
            on='Identifiant cas',  # Clé de jointure
            how='left'  # Garder tous les cas du reporting
        )
        
        print(f"[OK] Fusion terminée: {len(merged_df):,} lignes résultantes")
        
        # POST-TRAITEMENT : Utiliser préférentiellement les données de l'extract
        logging.info("=== POST-TRAITEMENT FUSION ===")
        for col in list(merged_df.columns):
            if col.endswith('_from_extract'):
                base_col = col.replace('_from_extract', '')
                if base_col in merged_df.columns:
                    # Remplacer les valeurs vides du reporting par celles de l'extract
                    mask = (merged_df[base_col].isna()) | (merged_df[base_col] == '')
                    if mask.sum() > 0:
                        merged_df.loc[mask, base_col] = merged_df.loc[mask, col]
                        logging.info(f"Colonnes '{base_col}' enrichie avec {mask.sum()} valeurs de l'extract")
                    # Supprimer la colonne temporaire de l'extract
                    merged_df.drop(columns=[col], inplace=True)
                else:
                    # Renommer la colonne de l'extract si elle n'a pas d'équivalent dans le reporting
                    merged_df.rename(columns={col: base_col}, inplace=True)
                    logging.info(f"Colonne '{col}' renommée en '{base_col}'")
        
        # Gérer spécifiquement la colonne Région (au cas où)
        if 'Region' in merged_df.columns and 'Région' not in merged_df.columns:
            merged_df.rename(columns={'Region': 'Région'}, inplace=True)
            logging.info("Colonne Region renommée en Région")
        
        # ANALYSE DE LA QUALITÉ DU CROISEMENT
        logging.info("=== ANALYSE DE LA QUALITÉ DU CROISEMENT ===")
        total_before = len(df_reporting)  # Base : fichier reporting
        total_after = len(merged_df)
        
        # Vérifier combien de lignes ont été perdues/gagnées
        logging.info(f"Lignes dans reporting (base): {total_before}")
        logging.info(f"Lignes après enrichissement: {total_after}")
        
        if total_after != total_before:
            diff = total_after - total_before
            logging.warning(f"Différence: {diff} lignes ({'gain' if diff > 0 else 'perte'})")
        else:
            logging.info("[OK] Aucune ligne perdue lors de l'enrichissement")
        
        # Analyser les colonnes importantes après jointure
        key_columns = ['Identifiant cas', 'TT STIT', 'PM', 'STIT', 'Région', 'Ref_MUT', 'OC', 'Diagnostic_OC']
        for col in key_columns:
            if col in merged_df.columns:
                non_null = merged_df[col].notna().sum()
                empty_string = (merged_df[col] == '').sum()
                filled = non_null - empty_string
                logging.info(f"Colonne '{col}': {filled}/{total_after} remplies ({filled/total_after*100:.1f}%)")
                
                if filled < total_after * 0.5:  # Si moins de 50% sont remplies
                    logging.warning(f"[ATTENTION] Colonne '{col}' peu remplie ({filled/total_after*100:.1f}%)")
            else:
                logging.warning(f"[ERREUR] Colonne '{col}' manquante après fusion")
        
        # Vérifier spécifiquement pour Ref_MUT car c'est crucial pour la logique de réitération
        if 'Ref_MUT' in merged_df.columns:
            ref_mut_filled = merged_df['Ref_MUT'].notna().sum()
            ref_mut_not_empty = (merged_df['Ref_MUT'] != '').sum()
            ref_mut_useful = (merged_df['Ref_MUT'].notna() & (merged_df['Ref_MUT'] != '')).sum()
            
            logging.info(f"[REF_MUT] Ref_MUT - Non nulles: {ref_mut_filled}, Non vides: {ref_mut_not_empty}, Utiles: {ref_mut_useful}")
            
            if ref_mut_useful < total_after * 0.3:  # Si moins de 30% des Ref_MUT sont utiles
                logging.warning(f"[ATTENTION] ATTENTION: Seulement {ref_mut_useful/total_after*100:.1f}% des Ref_MUT sont utilisables!")
                logging.warning("   Cela peut impacter la logique de réitération STT!")
            else:
                logging.info(f"[OK] {ref_mut_useful/total_after*100:.1f}% des Ref_MUT sont utilisables pour la réitération")
        
        # S'ASSURER QUE TOUTES LES COLONNES REQUISES EXISTENT
        # AJOUT CRITICAL: Inclure les colonnes de signalisation
        required_columns = ['TT STIT', 'PM', 'Région', 'STIT', 'DEPT', 'Propriétaire',
                           'Ex_type', 'Ex_source', 'Ex_famille', 'Ex_diagnostic', 
                           'Date_resolution_SIG', 'Date_New_exp', 'New_EXP', 
                           'Semaine résol sig', 'Mois résol sig', 'Semaine ouv new exp', 
                           'Mois ouv new exp', 'Délai_JC2_Resol sig', 'Délai_JC3_Resol exp', 'Statut_SIG']
        for col in required_columns:
            if col not in merged_df.columns:
                merged_df[col] = ''
                logging.info(f"Colonne '{col}' créée (manquante après fusion)")
        
        # ENRICHISSEMENT SUPPLÉMENTAIRE AVEC LE FICHIER STIT
        if df_stit is not None:
            logging.info("=== ENRICHISSEMENT AVEC _FTTH_TT_STIT.xlsx ===")
            print("⏳ Enrichissement avec _FTTH_TT_STIT.xlsx...")
            
            # Vérifier les colonnes disponibles dans le fichier STIT
            stit_columns = list(df_stit.columns)
            logging.info(f"Colonnes disponibles dans _FTTH_TT_STIT.xlsx: {stit_columns}")
            
            # Créer un dictionnaire pour l'enrichissement basé sur 'TT STIT'
            stit_dict = {}
            if 'TT STIT' in df_stit.columns:
                for idx, row in df_stit.iterrows():
                    tt_stit = row.get('TT STIT')
                    if tt_stit and tt_stit != '':
                        stit_dict[tt_stit] = row.to_dict()
                
                logging.info(f"Dictionnaire STIT créé avec {len(stit_dict)} entrées basées sur 'TT STIT'")
                
                # Enrichir le champ STIT avec 'Sous-traitant intervention' du fichier STIT
                if 'Sous-traitant intervention' in stit_columns:
                    stit_empty_mask = (merged_df['STIT'].isna()) | (merged_df['STIT'] == '')
                    stit_empty_count = stit_empty_mask.sum()
                    
                    if stit_empty_count > 0:
                        logging.info(f"Tentative d'enrichissement de {stit_empty_count} champs STIT vides...")
                        filled_count = 0
                        
                        for idx in merged_df[stit_empty_mask].index:
                            tt_stit_value = merged_df.at[idx, 'TT STIT']
                            if tt_stit_value and tt_stit_value in stit_dict:
                                sous_traitant = stit_dict[tt_stit_value].get('Sous-traitant intervention')
                                if sous_traitant and sous_traitant != '':
                                    # Appliquer l'harmonisation
                                    harmonized_stit = harmonize_stit_name(sous_traitant)
                                    merged_df.at[idx, 'STIT'] = harmonized_stit
                                    filled_count += 1
                        
                        logging.info(f"STIT enrichi: {filled_count} champs remplis depuis _FTTH_TT_STIT.xlsx")
                        print(f"✓ STIT enrichi: {filled_count} champs remplis")
                    else:
                        logging.info("Aucun champ STIT vide à enrichir")
                else:
                    logging.warning("Colonne 'Sous-traitant intervention' non trouvée dans _FTTH_TT_STIT.xlsx")
            else:
                logging.warning("Colonne 'TT STIT' non trouvée dans _FTTH_TT_STIT.xlsx pour la jointure")
        
        # ENRICHISSEMENT SUPPLÉMENTAIRE AVEC LE FICHIER ANALYSE_STIT_MANQUANT
        if df_analyse_manquant is not None:
            logging.info("=== ENRICHISSEMENT AVEC Analyse_STIT_manquant.xlsx ===")
            print("⏳ Enrichissement avec Analyse_STIT_manquant.xlsx...")
            
            # Vérifier les colonnes disponibles dans le fichier
            analyse_columns = list(df_analyse_manquant.columns)
            logging.info(f"Colonnes disponibles dans Analyse_STIT_manquant.xlsx: {analyse_columns}")
            
            # Créer un dictionnaire pour l'enrichissement basé sur 'TT STIT'
            analyse_dict = {}
            if 'TT STIT' in df_analyse_manquant.columns:
                for idx, row in df_analyse_manquant.iterrows():
                    tt_stit = row.get('TT STIT')
                    if tt_stit and tt_stit != '':
                        analyse_dict[tt_stit] = row.to_dict()
                
                logging.info(f"Dictionnaire Analyse_STIT_manquant créé avec {len(analyse_dict)} entrées basées sur 'TT STIT'")
                
                # Enrichir les champs STIT et PM avec les données du fichier d'analyse
                fields_to_enrich = []
                if 'STIT' in analyse_columns:
                    fields_to_enrich.append(('STIT', 'STIT'))
                if 'PM' in analyse_columns:
                    fields_to_enrich.append(('PM', 'PM'))
                
                for target_field, source_field in fields_to_enrich:
                    empty_mask = (merged_df[target_field].isna()) | (merged_df[target_field] == '')
                    empty_count = empty_mask.sum()
                    
                    if empty_count > 0:
                        logging.info(f"Tentative d'enrichissement de {empty_count} champs {target_field} vides...")
                        filled_count = 0
                        
                        for idx in merged_df[empty_mask].index:
                            tt_stit_value = merged_df.at[idx, 'TT STIT']
                            if tt_stit_value and tt_stit_value in analyse_dict:
                                value = analyse_dict[tt_stit_value].get(source_field)
                                if value and value != '':
                                    # Appliquer l'harmonisation pour le champ STIT
                                    if target_field == 'STIT':
                                        value = harmonize_stit_name(value)
                                    
                                    merged_df.at[idx, target_field] = value
                                    filled_count += 1
                        
                        logging.info(f"{target_field} enrichi: {filled_count} champs remplis depuis Analyse_STIT_manquant.xlsx")
                        print(f"✓ {target_field} enrichi: {filled_count} champs remplis")
                    else:
                        logging.info(f"Aucun champ {target_field} vide à enrichir")
            else:
                logging.warning("Colonne 'TT STIT' non trouvée dans Analyse_STIT_manquant.xlsx pour la jointure")
        
        # ENRICHISSEMENT AVEC LE FICHIER FTTH_Liste_PM_source_sadira
        if df_pm_sadira is not None:
            logging.info("=== ENRICHISSEMENT AVEC FTTH_Liste_PM_source_sadira.xlsx ===")
            print("⏳ Enrichissement avec FTTH_Liste_PM_source_sadira.xlsx...")
            
            # Vérifier les colonnes disponibles dans le fichier PM Sadira
            sadira_columns = list(df_pm_sadira.columns)
            logging.info(f"Colonnes disponibles dans FTTH_Liste_PM_source_sadira.xlsx: {sadira_columns}")
            
            # Créer un dictionnaire pour l'enrichissement basé sur 'Référence - LT.DET'
            sadira_dict = {}
            if 'Référence - LT.DET' in df_pm_sadira.columns:
                for idx, row in df_pm_sadira.iterrows():
                    ref_ltdet = row.get('Référence - LT.DET')
                    if ref_ltdet and ref_ltdet != '':
                        sadira_dict[ref_ltdet] = row.to_dict()
                
                logging.info(f"Dictionnaire FTTH_Liste_PM_source_sadira créé avec {len(sadira_dict)} entrées basées sur 'Référence - LT.DET'")
                
                # Enrichir les champs PM et Propriétaire avec les données du fichier Sadira
                # Croisement: PM du fichier principal = 'Référence - LT.DET' du fichier Sadira
                
                # 1. Enrichir les PM vides en utilisant PM comme clé de recherche
                pm_empty_mask = (merged_df['PM'].isna()) | (merged_df['PM'] == '')
                pm_empty_count = pm_empty_mask.sum()
                
                if pm_empty_count > 0:
                    logging.info(f"Tentative d'enrichissement de {pm_empty_count} champs PM vides...")
                    pm_filled_count = 0
                    
                    # Note: Ici on ne peut pas enrichir les PM vides car on a besoin du PM pour faire la jointure
                    # Cette section reste pour la cohérence mais ne fera rien
                    logging.info("PM vides détectés mais impossible d'enrichir (PM requis pour la jointure)")
                
                # 2. Enrichir les champs Propriétaire en utilisant PM comme clé
                proprietaire_empty_mask = (merged_df['Propriétaire'].isna()) | (merged_df['Propriétaire'] == '')
                proprietaire_empty_count = proprietaire_empty_mask.sum()
                
                if proprietaire_empty_count > 0 and 'Propriétaire - LT.DET' in sadira_columns:
                    logging.info(f"Tentative d'enrichissement de {proprietaire_empty_count} champs Propriétaire vides...")
                    proprietaire_filled_count = 0
                    
                    for idx in merged_df[proprietaire_empty_mask].index:
                        pm_value = merged_df.at[idx, 'PM']
                        if pm_value and pm_value in sadira_dict:
                            proprietaire_sadira = sadira_dict[pm_value].get('Propriétaire - LT.DET')
                            if proprietaire_sadira and proprietaire_sadira != '':
                                merged_df.at[idx, 'Propriétaire'] = proprietaire_sadira
                                proprietaire_filled_count += 1
                    
                    logging.info(f"Propriétaire enrichi: {proprietaire_filled_count} champs remplis depuis FTTH_Liste_PM_source_sadira.xlsx")
                    print(f"✓ Propriétaire enrichi: {proprietaire_filled_count} champs remplis")
                else:
                    if proprietaire_empty_count == 0:
                        logging.info("Aucun champ Propriétaire vide à enrichir")
                    if 'Propriétaire - LT.DET' not in sadira_columns:
                        logging.warning("Colonne 'Propriétaire - LT.DET' non trouvée dans FTTH_Liste_PM_source_sadira.xlsx")
                
                # 3. Enrichir les champs DEPT en utilisant Code postal - LT.ADR (2 premiers digits)
                dept_empty_mask = (merged_df['DEPT'].isna()) | (merged_df['DEPT'] == '')
                dept_empty_count = dept_empty_mask.sum()
                
                if dept_empty_count > 0 and 'Code postal - LT.ADR' in sadira_columns:
                    logging.info(f"Tentative d'enrichissement de {dept_empty_count} champs DEPT vides...")
                    dept_filled_count = 0
                    
                    for idx in merged_df[dept_empty_mask].index:
                        pm_value = merged_df.at[idx, 'PM']
                        if pm_value and pm_value in sadira_dict:
                            code_postal = sadira_dict[pm_value].get('Code postal - LT.ADR')
                            if code_postal and len(str(code_postal)) >= 2:
                                # Extraire les 2 premiers digits du code postal
                                dept_code = str(code_postal)[:2]
                                merged_df.at[idx, 'DEPT'] = dept_code
                                dept_filled_count += 1
                    
                    logging.info(f"DEPT enrichi: {dept_filled_count} champs remplis depuis FTTH_Liste_PM_source_sadira.xlsx")
                    print(f"✓ DEPT enrichi: {dept_filled_count} champs remplis")
                else:
                    if dept_empty_count == 0:
                        logging.info("Aucun champ DEPT vide à enrichir")
                    if 'Code postal - LT.ADR' not in sadira_columns:
                        logging.warning("Colonne 'Code postal - LT.ADR' non trouvée dans FTTH_Liste_PM_source_sadira.xlsx")
                
                # 4. Enrichir les champs Région en utilisant Libellé Région - LT.ADR avec normalisation
                region_empty_mask = (merged_df['Région'].isna()) | (merged_df['Région'] == '')
                region_empty_count = region_empty_mask.sum()
                
                if region_empty_count > 0 and 'Libellé Région - LT.ADR' in sadira_columns:
                    logging.info(f"Tentative d'enrichissement de {region_empty_count} champs Région vides...")
                    region_filled_count = 0
                    
                    for idx in merged_df[region_empty_mask].index:
                        pm_value = merged_df.at[idx, 'PM']
                        if pm_value and pm_value in sadira_dict:
                            region_libelle = sadira_dict[pm_value].get('Libellé Région - LT.ADR')
                            if region_libelle and region_libelle != '':
                                # Normaliser le nom de région
                                normalized_region = normalize_region_name(region_libelle)
                                merged_df.at[idx, 'Région'] = normalized_region
                                region_filled_count += 1
                    
                    logging.info(f"Région enrichie: {region_filled_count} champs remplis depuis FTTH_Liste_PM_source_sadira.xlsx")
                    print(f"✓ Région enrichie: {region_filled_count} champs remplis")
                else:
                    if region_empty_count == 0:
                        logging.info("Aucun champ Région vide à enrichir")
                    if 'Libellé Région - LT.ADR' not in sadira_columns:
                        logging.warning("Colonne 'Libellé Région - LT.ADR' non trouvée dans FTTH_Liste_PM_source_sadira.xlsx")
                
                # 5. Enrichir les champs Zone en utilisant les informations du fichier Sadira
                zone_empty_mask = (merged_df['Zone'].isna()) | (merged_df['Zone'] == '')
                zone_empty_count = zone_empty_mask.sum()
                
                # Utiliser la colonne spécifique pour Zone
                zone_source_column = 'Type de zone (ZTD/ZMD) – LT.DET'
                
                if zone_empty_count > 0 and zone_source_column in sadira_columns:
                    logging.info(f"Tentative d'enrichissement de {zone_empty_count} champs Zone vides...")
                    logging.info(f"Utilisation de la colonne '{zone_source_column}' du fichier Sadira")
                    zone_filled_count = 0
                    
                    for idx in merged_df[zone_empty_mask].index:
                        pm_value = merged_df.at[idx, 'PM']
                        if pm_value and pm_value in sadira_dict:
                            zone_value = sadira_dict[pm_value].get(zone_source_column)
                            if zone_value and zone_value != '':
                                merged_df.at[idx, 'Zone'] = zone_value
                                zone_filled_count += 1
                    
                    logging.info(f"Zone enrichie: {zone_filled_count} champs remplis depuis FTTH_Liste_PM_source_sadira.xlsx")
                    print(f"✓ Zone enrichie: {zone_filled_count} champs remplis")
                else:
                    if zone_empty_count == 0:
                        logging.info("Aucun champ Zone vide à enrichir")
                    if zone_source_column not in sadira_columns:
                        logging.warning(f"Colonne '{zone_source_column}' non trouvée dans FTTH_Liste_PM_source_sadira.xlsx")
                        logging.info(f"Colonnes disponibles: {sadira_columns}")
            else:
                logging.warning("Colonne 'Référence - LT.DET' non trouvée dans FTTH_Liste_PM_source_sadira.xlsx pour la jointure")
        
        # Ajouter le fichier Sadira comme optionnel dans la gestion des erreurs
        if pm_sadira_path and not pm_sadira_path.exists():
            logging.info("Le fichier FTTH_Liste_PM_source_sadira.xlsx est optionnel, continuons sans lui")
        
        # ENRICHISSEMENT AVEC LE FICHIER LISTE_OI
        if df_liste_oi is not None:
            logging.info("=== ENRICHISSEMENT AVEC Liste_OI.xlsx ===")
            print("⏳ Enrichissement avec Liste_OI.xlsx...")
            
            # Vérifier les colonnes disponibles dans le fichier Liste_OI
            liste_oi_columns = list(df_liste_oi.columns)
            logging.info(f"Colonnes disponibles dans Liste_OI.xlsx: {liste_oi_columns}")
            
            # Créer un dictionnaire pour l'enrichissement basé sur 'Propriétaire - LTDET'
            liste_oi_dict = {}
            if 'Propriétaire - LTDET' in df_liste_oi.columns:
                for idx, row in df_liste_oi.iterrows():
                    proprietaire_ltdet = row.get('Propriétaire - LTDET')
                    if proprietaire_ltdet and proprietaire_ltdet != '':
                        liste_oi_dict[proprietaire_ltdet] = row.to_dict()
                
                logging.info(f"Dictionnaire Liste_OI créé avec {len(liste_oi_dict)} entrées basées sur 'Propriétaire - LTDET'")
                
                # Enrichir les champs avec les données du fichier Liste_OI
                # Croisement: Propriétaire du fichier principal = 'Propriétaire - LTDET' du fichier Liste_OI
                
                # Champs à enrichir avec leurs sources correspondantes
                fields_mapping = [
                    ('Zone', 'Zone'),  # Si Zone existe dans Liste_OI
                    ('Code OI InterOP', 'Code OI InterOP'),
                    ('OI', 'OI')
                ]
                
                # Vérifier quels champs peuvent être enrichis
                available_fields = []
                for target_field, source_field in fields_mapping:
                    if source_field in liste_oi_columns:
                        available_fields.append((target_field, source_field))
                    else:
                        logging.warning(f"Colonne '{source_field}' non trouvée dans Liste_OI.xlsx")
                
                # Enrichir chaque champ disponible
                for target_field, source_field in available_fields:
                    empty_mask = (merged_df[target_field].isna()) | (merged_df[target_field] == '')
                    empty_count = empty_mask.sum()
                    
                    if empty_count > 0:
                        logging.info(f"Tentative d'enrichissement de {empty_count} champs {target_field} vides...")
                        filled_count = 0
                        
                        for idx in merged_df[empty_mask].index:
                            proprietaire_value = merged_df.at[idx, 'Propriétaire']
                            if proprietaire_value and proprietaire_value in liste_oi_dict:
                                value = liste_oi_dict[proprietaire_value].get(source_field)
                                if value and value != '':
                                    merged_df.at[idx, target_field] = value
                                    filled_count += 1
                        
                        logging.info(f"{target_field} enrichi: {filled_count} champs remplis depuis Liste_OI.xlsx")
                        print(f"✓ {target_field} enrichi: {filled_count} champs remplis")
                    else:
                        logging.info(f"Aucun champ {target_field} vide à enrichir")
                
                # Enrichir aussi le Propriétaire si vide (cas particulier)
                proprietaire_empty_mask = (merged_df['Propriétaire'].isna()) | (merged_df['Propriétaire'] == '')
                proprietaire_empty_count = proprietaire_empty_mask.sum()
                
                if proprietaire_empty_count > 0:
                    logging.info(f"Tentative d'enrichissement de {proprietaire_empty_count} champs Propriétaire vides...")
                    proprietaire_filled_count = 0
                    
                    # Pour le Propriétaire, on peut essayer d'autres champs comme clés de recherche
                    # Par exemple, utiliser OI ou Code OI InterOP comme clé inverse
                    logging.info("Enrichissement Propriétaire: nécessiterait une logique de croisement inverse")
                    # Cette logique pourrait être ajoutée si nécessaire
                
            else:
                logging.warning("Colonne 'Propriétaire - LTDET' non trouvée dans Liste_OI.xlsx pour la jointure")
        
        # ENRICHISSEMENT AVEC LE FICHIER UPDATE_BASE_MANQUANT_REF_MUT
        if df_update_ref_mut is not None:
            logging.info("=== ENRICHISSEMENT AVEC update_Base_manquant_REF_MUT.xlsx ===")
            print("⏳ Enrichissement avec update_Base_manquant_REF_MUT.xlsx...")
            
            # Vérifier les colonnes disponibles dans le fichier
            update_ref_mut_columns = list(df_update_ref_mut.columns)
            logging.info(f"Colonnes disponibles dans update_Base_manquant_REF_MUT.xlsx: {update_ref_mut_columns}")
            
            # Les dictionnaires ont déjà été créés lors du chargement du fichier
            
            # Enrichissement 1: PM vides via Ref_MUT → Sro
            if ref_mut_dict and 'Sro' in update_ref_mut_columns:
                pm_empty_mask = (merged_df['PM'].isna()) | (merged_df['PM'] == '')
                pm_empty_count = pm_empty_mask.sum()
                
                if pm_empty_count > 0:
                    logging.info(f"Tentative d'enrichissement de {pm_empty_count} champs PM vides via Ref_MUT...")
                    pm_filled_count = 0
                    
                    for idx in merged_df[pm_empty_mask].index:
                        ref_mut_value = merged_df.at[idx, 'Ref_MUT']
                        if ref_mut_value and ref_mut_value in ref_mut_dict:
                            sro_value = ref_mut_dict[ref_mut_value].get('Sro')
                            if sro_value and sro_value != '':
                                merged_df.at[idx, 'PM'] = sro_value
                                pm_filled_count += 1
                    
                    logging.info(f"PM enrichi: {pm_filled_count} champs remplis via Ref_MUT → Sro")
                    print(f"✓ PM enrichi: {pm_filled_count} champs remplis")
            
            # Enrichissement 2: Code OI InterOP vides via PM → Code OI - ACV
            if pm_dict and 'Code OI - ACV' in update_ref_mut_columns:
                code_oi_empty_mask = (merged_df['Code OI InterOP'].isna()) | (merged_df['Code OI InterOP'] == '')
                code_oi_empty_count = code_oi_empty_mask.sum()
                
                if code_oi_empty_count > 0:
                    logging.info(f"Tentative d'enrichissement de {code_oi_empty_count} champs Code OI InterOP vides via PM...")
                    code_oi_filled_count = 0
                    
                    for idx in merged_df[code_oi_empty_mask].index:
                        pm_value = merged_df.at[idx, 'PM']
                        if pm_value and pm_value in pm_dict:
                            code_oi_value = pm_dict[pm_value].get('Code OI - ACV')
                            if code_oi_value and code_oi_value != '':
                                merged_df.at[idx, 'Code OI InterOP'] = code_oi_value
                                code_oi_filled_count += 1
                    
                    logging.info(f"Code OI InterOP enrichi: {code_oi_filled_count} champs remplis via PM → Code OI - ACV")
                    print(f"✓ Code OI InterOP enrichi: {code_oi_filled_count} champs remplis")
            
            # Enrichissement 3: OI vides (nécessiterait une logique supplémentaire ou un autre fichier de correspondance)
            # Pour l'instant, on log que cette fonctionnalité pourrait être ajoutée
            oi_empty_mask = (merged_df['OI'].isna()) | (merged_df['OI'] == '')
            oi_empty_count = oi_empty_mask.sum()
            if oi_empty_count > 0:
                logging.info(f"OI vides détectés: {oi_empty_count}. Enrichissement OI nécessiterait une correspondance Code OI → OI")
        
        # ENRICHISSEMENT ZONE INTELLIGENTE BASÉE SUR DEPT
        zone_empty_mask = (merged_df['Zone'].isna()) | (merged_df['Zone'] == '')
        zone_empty_count = zone_empty_mask.sum()
        
        if zone_empty_count > 0:
            logging.info("=== ENRICHISSEMENT ZONE INTELLIGENTE ===")
            print("⏳ Détermination intelligente des zones ZTD/ZMD basée sur DEPT...")
            logging.info(f"Tentative de détermination de {zone_empty_count} zones vides basée sur DEPT...")
            
            zone_filled_count = 0
            for idx in merged_df[zone_empty_mask].index:
                dept_value = merged_df.at[idx, 'DEPT']
                if dept_value and dept_value != '':
                    determined_zone = determine_zone_from_dept(dept_value)
                    if determined_zone:
                        merged_df.at[idx, 'Zone'] = determined_zone
                        zone_filled_count += 1
            
            logging.info(f"Zone enrichie intelligemment: {zone_filled_count} champs remplis basés sur DEPT")
            print(f"✓ Zone enrichie intelligemment: {zone_filled_count} champs remplis")
        
        # ENRICHISSEMENT FINAL AMÉLIORÉ AVEC LOGIQUE SPÉCIALISÉE
        logging.info("=== ENRICHISSEMENT FINAL AMÉLIORÉ ===")
        print("⏳ Enrichissement final amélioré des champs restants...")
        
        # Appel de la fonction de completion améliorée
        merged_df = enhanced_field_completion(
            merged_df=merged_df,
            df_liste_oi=df_liste_oi,
            ref_mut_dict=ref_mut_dict
        )
        
        # Le dédoublement sera fait après le merge avec l'extract (plus bas)
        
        # HARMONISATION DES NOMS STIT
        if 'STIT' in merged_df.columns:
            logging.info("=== HARMONISATION DES NOMS STIT ===")
            print("⏳ Harmonisation des noms STIT...")
            
            # Compter les valeurs avant harmonisation
            stit_before = merged_df['STIT'].value_counts()
            logging.info(f"Valeurs STIT avant harmonisation: {len(stit_before)} valeurs uniques")
            
            # Appliquer l'harmonisation
            merged_df['STIT'] = merged_df['STIT'].apply(harmonize_stit_name)
            
            # Compter les valeurs après harmonisation
            stit_after = merged_df['STIT'].value_counts()
            logging.info(f"Valeurs STIT après harmonisation: {len(stit_after)} valeurs uniques")
            
            # Log des changements principaux
            logging.info("Répartition des STIT harmonisés:")
            for stit, count in stit_after.head(10).items():
                if pd.notna(stit) and stit != '':
                    logging.info(f"  {stit}: {count} cas")
            
            print(f"✓ STIT harmonisés: {len(stit_before)} → {len(stit_after)} valeurs uniques")
        
        # FILTRAGE DES LIGNES AVEC TT STIT VIDE
        if 'TT STIT' in merged_df.columns:
            logging.info("=== FILTRAGE DES LIGNES AVEC TT STIT VIDE ===")
            print("⏳ Suppression des lignes avec TT STIT vide...")
            
            # Compter les lignes avant filtrage
            total_before_filter = len(merged_df)
            
            # Identifier les lignes avec TT STIT vide
            tt_stit_empty_mask = (merged_df['TT STIT'].isna()) | (merged_df['TT STIT'] == '')
            empty_count = tt_stit_empty_mask.sum()
            
            if empty_count > 0:
                logging.info(f"Lignes avec TT STIT vide trouvées: {empty_count} sur {total_before_filter}")
                
                # Supprimer les lignes avec TT STIT vide
                merged_df = merged_df[~tt_stit_empty_mask].copy()
                
                total_after_filter = len(merged_df)
                removed_count = total_before_filter - total_after_filter
                
                logging.info(f"Lignes supprimées: {removed_count}")
                logging.info(f"Lignes restantes: {total_after_filter}")
                print(f"✓ {removed_count} lignes avec TT STIT vide supprimées")
                print(f"✓ {total_after_filter} lignes conservées")
            else:
                logging.info("Aucune ligne avec TT STIT vide trouvée")
                print("✓ Aucune ligne avec TT STIT vide à supprimer")
        
        # ALIMENTATION DES CHAMPS VIDES AVEC LES SOURCES ALTERNATIVES
        logging.info("=== ALIMENTATION DES CHAMPS VIDES ===")
        
        # Alimenter TOUS les champs vides en utilisant l'Identifiant cas comme clé principale
        # Créer un dictionnaire maître avec toutes les données du fichier extract indexées par Identifiant cas
        logging.info("Création du dictionnaire maître basé sur l'Identifiant cas...")
        
        if 'Identifiant cas' in df_extract.columns:
            # DIAGNOSTIC : Vérifier les colonnes disponibles dans le fichier extract
            logging.info(f"[DIAGNOSTIC] Colonnes dans le fichier extract: {list(df_extract.columns)}")
            
            # Créer un dictionnaire de référence complet depuis le fichier extract
            extract_dict = {}
            for idx, row in df_extract.iterrows():
                identifiant = row.get('Identifiant cas')
                if identifiant and identifiant != '':
                    extract_dict[identifiant] = row.to_dict()
            
            logging.info(f"Dictionnaire maître créé avec {len(extract_dict)} entrées uniques")
            
            # DIAGNOSTIC : Vérifier quelques exemples du dictionnaire
            if extract_dict:
                sample_key = next(iter(extract_dict))
                sample_data = extract_dict[sample_key]
                logging.info(f"[DIAGNOSTIC] Exemple d'entrée dans le dictionnaire pour '{sample_key}':")
                for key, value in list(sample_data.items())[:10]:  # Première 10 colonnes seulement
                    logging.info(f"  {key}: {value}")
            
            # Alimenter tous les champs vides en utilisant ce dictionnaire
            # AJOUT CRITICAL: Inclure les colonnes de signalisation pour le dédoublement !
            fields_to_complete = ['TT STIT', 'PM', 'Région', 'STIT', 'DEPT', 'Propriétaire', 
                                 'Ex_type', 'Ex_source', 'Ex_famille', 'Ex_diagnostic', 
                                 'Date_resolution_SIG', 'Date_New_exp', 'New_EXP', 
                                 'Semaine résol sig', 'Mois résol sig', 'Semaine ouv new exp', 
                                 'Mois ouv new exp', 'Délai_JC2_Resol sig', 'Délai_JC3_Resol exp', 'Statut_SIG']
            
            for field in fields_to_complete:
                if field in merged_df.columns:
                    empty_mask = (merged_df[field].isna()) | (merged_df[field] == '')
                    empty_count = empty_mask.sum()
                    
                    if empty_count > 0:
                        filled_count = 0
                        
                        # Correspondances de colonnes entre extract et final
                        extract_field_mapping = {
                            'TT STIT': ['A pour cas parent', 'TT STIT'],  # A pour cas parent en priorité puis TT STIT
                            'PM': ['Elément impacté', 'PM'],
                            'Région': ['Region', 'Région'],
                            'STIT': ['Sous-traitant intervention', 'STIT'],
                            'DEPT': ['DEPT'],
                            'Propriétaire': ['Propriétaire', 'Propriétaire - LTDET'],
                            # AJOUT CRITICAL: Mapping des colonnes de signalisation
                            'Ex_type': ['Ex_type'],
                            'Ex_source': ['Ex_source'],
                            'Ex_famille': ['Ex_famille'],
                            'Ex_diagnostic': ['Ex_diagnostic'],
                            'Date_resolution_SIG': ['Date_resolution_SIG'],
                            'Date_New_exp': ['Date_New_exp'],
                            'New_EXP': ['New_EXP'],
                            'Semaine résol sig': ['Semaine résol sig'],
                            'Mois résol sig': ['Mois résol sig'],
                            'Semaine ouv new exp': ['Semaine ouv new exp'],
                            'Mois ouv new exp': ['Mois ouv new exp'],
                            'Délai_JC2_Resol sig': ['Délai_JC2_Resol sig'],
                            'Délai_JC3_Resol exp': ['Délai_JC3_Resol exp'],
                            'Statut_SIG': ['Statut_SIG']
                        }
                        
                        possible_sources = extract_field_mapping.get(field, [field])
                        
                        # DIAGNOSTIC : Vérifier les sources disponibles
                        available_sources = []
                        if extract_dict:
                            sample_data = next(iter(extract_dict.values()))
                            for source in possible_sources:
                                if source in sample_data:
                                    available_sources.append(source)
                        
                        logging.info(f"[DIAGNOSTIC] Pour le champ '{field}': sources possibles {possible_sources}, disponibles {available_sources}")
                        
                        # Diagnostic spécial pour TT STIT
                        if field == 'TT STIT' and extract_dict:
                            sample_data = next(iter(extract_dict.values()))
                            logging.info(f"[TT STIT DIAGNOSTIC] Colonnes disponibles dans extract: {list(sample_data.keys())}")
                            if 'A pour cas parent' in sample_data:
                                sample_value = sample_data['A pour cas parent']
                                logging.info(f"[TT STIT DIAGNOSTIC] Exemple de valeur 'A pour cas parent': {sample_value}")
                        
                        for idx in merged_df[empty_mask].index:
                            identifiant = merged_df.at[idx, 'Identifiant cas']
                            if identifiant and identifiant in extract_dict:
                                extract_row = extract_dict[identifiant]
                                
                                # Essayer toutes les sources possibles pour ce champ
                                for source_field in possible_sources:
                                    if source_field in extract_row:
                                        value = extract_row[source_field]
                                        if pd.notna(value) and value != '':
                                            # Appliquer l'harmonisation pour le champ STIT
                                            if field == 'STIT':
                                                value = harmonize_stit_name(value)
                                            
                                            merged_df.at[idx, field] = value
                                            filled_count += 1
                                            if source_field == 'A pour cas parent':
                                                logging.info(f"[DIAGNOSTIC] {field} rempli avec 'A pour cas parent' pour cas {identifiant}: {value}")
                                            break  # Prendre la première valeur valide trouvée
                        
                        if filled_count > 0:
                            logging.info(f"{field}: {filled_count} champs vides alimentés depuis le fichier extract")
                        else:
                            logging.warning(f"{field}: Aucun champ vide n'a pu être alimenté")
        
        # RAPPORT FINAL DE L'ALIMENTATION DES CHAMPS
        logging.info("=== RAPPORT FINAL D'ALIMENTATION DES CHAMPS ===")
        key_fields = ['TT STIT', 'PM', 'Région', 'STIT', 'DEPT', 'Propriétaire']
        for field in key_fields:
            if field in merged_df.columns:
                non_empty = ((merged_df[field].notna()) & (merged_df[field] != '')).sum()
                total = len(merged_df)
                percentage = (non_empty / total * 100) if total > 0 else 0
                logging.info(f"[FINAL] {field}: {non_empty}/{total} remplis ({percentage:.1f}%)")
        
        logging.info("=== FIN ANALYSE CROISEMENT ===")

        # NORMALISATION GLOBALE DES FORMATS MOIS AVANT DÉDOUBLEMENT
        logging.info("=== NORMALISATION FORMATS MOIS ===")
        for mois_col in ['Mois_Création', 'Mois_Résolution']:
            if mois_col in merged_df.columns:
                before_samples = merged_df[mois_col].head(3).tolist()
                logging.info(f"[AVANT] {mois_col} exemples: {before_samples}")
                
                # Nettoyage robuste de tous types d'espaces
                merged_df[mois_col] = (merged_df[mois_col]
                    .astype(str)
                    .str.replace(' ', '', regex=False)     # Espace normal
                    .str.replace('\u00A0', '', regex=False) # Espace insécable
                    .str.replace('\u2009', '', regex=False) # Espace fine
                    .str.replace('\u202F', '', regex=False) # Espace insécable étroite
                    .str.replace(r'\s+', '', regex=True))   # Tous autres espaces
                
                after_samples = merged_df[mois_col].head(3).tolist()
                logging.info(f"[APRÈS] {mois_col} exemples: {after_samples}")
                
                # Compter les changements
                changes = sum(1 for b, a in zip(before_samples, after_samples) if str(b) != str(a))
                if changes > 0:
                    logging.info(f"{mois_col}: {changes} valeur(s) normalisée(s) sur les premiers échantillons")

        # DÉDOUBLEMENT DES TICKETS SIGNALISATION/EXPERTISE
        # IMPORTANT: Faire après le merge avec l'extract pour avoir accès aux colonnes Ex_type, Ex_source, etc.
        logging.info("=== DÉDOUBLEMENT SIGNALISATION/EXPERTISE ===")
        print("⏳ Dédoublement des tickets signalisation/expertise...")
        
        # DEBUG: Vérifier les données avant dédoublement
        before_count = len(merged_df)
        if 'Ex_type' in merged_df.columns:
            sig_count = (merged_df['Ex_type'] == 'SIGNALISATION').sum()
            logging.info(f"[DEBUG AVANT] Total lignes: {before_count}, Ex_type='SIGNALISATION': {sig_count}")
            
            # Vérifier C34048200 spécifiquement
            if 'Identifiant cas' in merged_df.columns:
                c34_lines = merged_df[merged_df['Identifiant cas'] == 'C34048200']
                if len(c34_lines) > 0:
                    c34_row = c34_lines.iloc[0]
                    logging.info(f"[DEBUG] C34048200 avant dédoublement: Type={c34_row.get('Type', 'N/A')}, Ex_type={c34_row.get('Ex_type', 'N/A')}")
        else:
            logging.warning("[DEBUG] Colonne Ex_type non trouvée avant dédoublement!")
        
        merged_df = duplicate_signalization_tickets(merged_df)
        
        # DEBUG: Vérifier les résultats après dédoublement  
        after_count = len(merged_df)
        logging.info(f"[DEBUG APRÈS] Total lignes: {before_count} -> {after_count} (différence: +{after_count - before_count})")
        
        if 'Identifiant cas' in merged_df.columns:
            c34_after = merged_df[merged_df['Identifiant cas'] == 'C34048200']
            logging.info(f"[DEBUG] C34048200 après dédoublement: {len(c34_after)} ligne(s)")
            for idx, row in c34_after.iterrows():
                logging.info(f"  C34048200: Type={row['Type']}")
        
        if 'Type' in merged_df.columns:
            type_counts_after = merged_df['Type'].value_counts()
            logging.info(f"[DEBUG] Types après dédoublement: {dict(type_counts_after)}")

        # Créer les colonnes manquantes nécessaires pour la structure finale
        logging.info("=== CRÉATION DES COLONNES MANQUANTES ===")
        
        # Colonnes requises qui pourraient manquer
        required_columns = {
            'Statut': '',
            'Ref OC': '',
            'OC': '',
            'Prise': '',
            'Diagnostic en cours': '',
            'Mois_Création': '',
            'Mois_Résolution': '',
            'Type': '',
            'Source_OC': '',
            'Famille_OC': '',
            'Commentaire Résolution': '',
            'TT STIT': '',
            'PM': '',
            'Région': '',
            'STIT': '',
            # AJOUT CRITICAL: Colonnes signalisation au cas où  
            'Ex_type': '',
            'Ex_source': '',
            'Ex_famille': '',
            'Ex_diagnostic': '',
            'Date_resolution_SIG': '',
            'Date_New_exp': '',
            'New_EXP': '',
            'Semaine résol sig': '',
            'Mois résol sig': '',
            'Semaine ouv new exp': '',
            'Mois ouv new exp': '',
            'Délai_JC2_Resol sig': '',
            'Délai_JC3_Resol exp': '',
            'Statut_SIG': '',
            'DEPT': '',
            'Propriétaire': '',
            'Zone': '',
            'Code OI InterOP': '',
            'OI': ''
        }
        
        # Créer seulement les colonnes qui n'existent pas encore
        for req_col, default_value in required_columns.items():
            if req_col not in merged_df.columns:
                merged_df[req_col] = default_value
                logging.info(f"• Colonne '{req_col}' créée avec valeur par défaut")
            else:
                logging.info(f"[OK] Colonne '{req_col}' déjà présente")
        
        logging.info("=== FIN CRÉATION COLONNES ===")

        merged_df.dropna(how='all', inplace=True)
        merged_df.fillna('', inplace=True)

        # Suppression des doublons basés sur la colonne "Identifiant cas" + "Type"
        # IMPORTANT: On utilise ['Identifiant cas', 'Type'] pour préserver les dédoublements SIGNALISATION/EXPERTISE
        if 'Identifiant cas' in merged_df.columns and 'Type' in merged_df.columns:
            # DEBUG: Vérifier C34048200 AVANT suppression doublons
            if 'Identifiant cas' in merged_df.columns:
                c34_before_dedup = merged_df[merged_df['Identifiant cas'] == 'C34048200']
                logging.info(f"[DEBUG AVANT DEDUP] C34048200: {len(c34_before_dedup)} ligne(s)")
                for idx, row in c34_before_dedup.iterrows():
                    logging.info(f"  C34048200: Type={row['Type']}")
            
            # Compter les doublons avant suppression
            total_rows = len(merged_df)
            # Supprimer les doublons en gardant la première occurrence (même Identifiant cas + même Type)
            merged_df.drop_duplicates(subset=['Identifiant cas', 'Type'], keep='first', inplace=True)
            # Compter combien de lignes ont été supprimées
            removed_rows = total_rows - len(merged_df)
            logging.info(f"Suppression de {removed_rows} doublons basés sur 'Identifiant cas' + 'Type'")
            logging.info("IMPORTANT: Doublons SIGNALISATION/EXPERTISE préservés grâce à la clé composite")
            
            # DEBUG: Vérifier C34048200 APRÈS suppression doublons
            if 'Identifiant cas' in merged_df.columns:
                c34_after_dedup = merged_df[merged_df['Identifiant cas'] == 'C34048200']
                logging.info(f"[DEBUG APRÈS DEDUP] C34048200: {len(c34_after_dedup)} ligne(s)")
                for idx, row in c34_after_dedup.iterrows():
                    logging.info(f"  C34048200: Type={row['Type']}")
        else:
            logging.warning("Colonnes 'Identifiant cas' ou 'Type' non trouvées, impossible de supprimer les doublons")

        # Supprimer les lignes où "Code OI InterOP" contient "SRRA"
        if 'Code OI InterOP' in merged_df.columns:
            # Identifier les lignes où "Code OI InterOP" contient "SRRA"
            srra_mask = merged_df['Code OI InterOP'].astype(str).str.contains('SRRA', na=False)
            srra_count = srra_mask.sum()

            # Supprimer ces lignes
            if srra_count > 0:
                merged_df = merged_df[~srra_mask]
                logging.info(f"Suppression de {srra_count} lignes où 'Code OI InterOP' contient 'SRRA'")
            else:
                logging.info("Aucune ligne avec 'SRRA' dans 'Code OI InterOP' n'a été trouvée")

        logging.info(f"Colonnes finales : {merged_df.columns.tolist()}")
        
        # Retourner les données fusionnées ET les données reporting pour le menu
        return sanitize_excel_output(merged_df), df_reporting

    except Exception as e:
        logging.error(f"ERREUR fusion : {str(e)}")
        logging.debug(traceback.format_exc())
        raise

def determine_facturation_eligibility(merged_df, selected_month=None):
    """Détermine l'éligibilité à la facturation selon les nouvelles règles STT avec logique de réitération.
       Un filtre sur le mois est appliqué après la création de la colonne Mois_Résolution.
       Seuls les tickets avec le statut "Clos" sont conservés."""
    logging.info("Détermination de l'éligibilité à la facturation avec nouvelles règles STT...")
    logging.info(f"Données d'entrée: {len(merged_df)} lignes")
    
    # CRÉER D'ABORD LES COLONNES DE MOIS si elles n'existent pas
    if 'Mois_Résolution' not in merged_df.columns:
        logging.info("Création de la colonne Mois_Résolution...")
        # Convertir les dates de résolution pour créer le mois
        if 'Date résolution' in merged_df.columns:
            # Copie de travail pour éviter de modifier l'original
            temp_dates = pd.to_datetime(merged_df['Date résolution'], errors='coerce')
            merged_df['Mois_Résolution'] = temp_dates.dt.strftime('%Y_%m')
            # Remplacer les NaT par des chaînes vides
            merged_df['Mois_Résolution'] = merged_df['Mois_Résolution'].fillna('')
            # NETTOYER LES ESPACES INSÉCABLES
            merged_df['Mois_Résolution'] = merged_df['Mois_Résolution'].str.replace('\xa0', '', regex=False)
            logging.info("Colonne Mois_Résolution créée à partir de Date résolution (espaces insécables supprimés)")
        else:
            logging.warning("Colonne 'Date résolution' non trouvée, impossible de créer Mois_Résolution")
            merged_df['Mois_Résolution'] = ''
    
    # Créer aussi Mois_Création si elle n'existe pas
    if 'Mois_Création' not in merged_df.columns:
        logging.info("Création de la colonne Mois_Création...")
        if 'Date ouverture du cas' in merged_df.columns:
            temp_dates = pd.to_datetime(merged_df['Date ouverture du cas'], errors='coerce')
            merged_df['Mois_Création'] = temp_dates.dt.strftime('%Y_%m')
            merged_df['Mois_Création'] = merged_df['Mois_Création'].fillna('')
            logging.info("Colonne Mois_Création créée à partir de Date ouverture du cas")
        else:
            merged_df['Mois_Création'] = ''
    
    # FILTRER PAR MOIS si spécifié
    if selected_month:
        try:
            # NETTOYER LA COLONNE AVANT FILTRAGE
            merged_df['Mois_Résolution'] = merged_df['Mois_Résolution'].astype(str)
            # Supprimer les espaces insécables
            merged_df['Mois_Résolution'] = merged_df['Mois_Résolution'].str.replace('\xa0', '', regex=False)
            
            # Vérifier le contenu de la colonne avant filtrage
            unique_months = merged_df['Mois_Résolution'].value_counts()
            logging.info(f"Mois disponibles avant filtrage: {dict(unique_months)}")
            
            # Appliquer le filtre
            before_filter = len(merged_df)
            merged_df = merged_df[merged_df['Mois_Résolution'] == str(selected_month)].copy()
            after_filter = len(merged_df)
            
            logging.info(f"Filtrage sur le mois {selected_month}: {before_filter} → {after_filter} lignes")
            
            if after_filter == 0:
                logging.error(f"PROBLÈME: Aucune donnée trouvée pour le mois {selected_month}!")
                logging.error("Vérifiez que le mois sélectionné correspond aux données disponibles")
                return merged_df  # Retourner le DataFrame vide pour debug
                
        except Exception as e:
            logging.error(f"Erreur lors du filtrage par mois: {e}")
            logging.debug(traceback.format_exc())

    # Filtrer pour ne garder que les tickets avec le statut "Clos"
    # IDENTIFIER LA BONNE COLONNE DE STATUT
    status_column = None
    possible_status_columns = ['En cours / Clos', 'Statut du cas', 'statut', 'Statut']
    
    for col in possible_status_columns:
        if col in merged_df.columns:
            # Vérifier s'il y a des valeurs non vides dans cette colonne
            non_empty_count = (merged_df[col].notna() & (merged_df[col] != '')).sum()
            if non_empty_count > 0:
                status_column = col
                logging.info(f"Colonne de statut trouvée: '{col}' avec {non_empty_count} valeurs non vides")
                break
    
    if status_column:
        # Afficher les valeurs uniques de la colonne de statut pour débogage
        unique_statuses = merged_df[status_column].unique()
        logging.info(f"Valeurs uniques dans la colonne {status_column}: {unique_statuses}")

        # Nettoyer la colonne de statut (supprimer les espaces, normaliser la casse)
        merged_df[status_column] = merged_df[status_column].astype(str).str.strip().str.title()

        # Afficher les valeurs uniques après nettoyage
        unique_statuses_after = merged_df[status_column].unique()
        logging.info(f"Valeurs uniques dans la colonne {status_column} après nettoyage: {unique_statuses_after}")

        # Filtrer pour ne garder que les tickets avec le statut "Clos"
        total_rows = len(merged_df)
        # Avant de filtrer, vérifier s'il y a des valeurs "Clos" dans les données
        clos_mask = merged_df[status_column].str.contains('Clos', case=False, na=False)
        clos_count = clos_mask.sum()
        
        if clos_count > 0:
            merged_df = merged_df[clos_mask].copy()
            filtered_rows = total_rows - len(merged_df)
            logging.info(f"Filtrage sur le statut 'Clos' avec la colonne '{status_column}': {filtered_rows} lignes supprimées, {len(merged_df)} lignes restantes")
            
            # Vérifier qu'il ne reste plus de tickets avec d'autres statuts
            remaining_statuses = merged_df[status_column].unique()
            logging.info(f"Statuts restants après filtrage: {remaining_statuses}")
        else:
            logging.warning(f"Aucun ticket avec le statut 'Clos' trouvé. Total des lignes: {total_rows}")
            logging.warning("Aucun filtrage appliqué - toutes les lignes seront conservées")
            # Ne pas filtrer si aucune ligne ne contient 'Clos'
        
        # Copier les valeurs dans la colonne 'Statut' finale pour la structure
        merged_df['Statut'] = merged_df[status_column]
    else:
        logging.warning("Aucune colonne de statut utilisable trouvée. Colonnes disponibles: " + str(list(merged_df.columns)))
        logging.warning("ATTENTION: Pas de filtrage sur le statut appliqué!")
        # Créer une colonne 'Statut' avec la valeur 'Clos' par défaut si elle n'existe pas
        merged_df['Statut'] = 'Clos'

    # Convertir les colonnes de date à DateTime
    for col in ['Date ouverture du cas', 'Date résolution']:
        try:
            # Vérifier si la colonne existe
            if col in merged_df.columns:
                # Sauvegarder la colonne originale avant conversion
                original_col = merged_df[col].copy()

                # Afficher quelques valeurs pour débogage
                logging.info(f"Exemples de valeurs dans la colonne {col} avant conversion: {merged_df[col].head(5).tolist()}")

                # Compter les valeurs non nulles avant conversion
                non_null_before = merged_df[col].notna().sum()
                logging.info(f"Avant conversion: {non_null_before} valeurs non nulles sur {len(merged_df)} lignes dans {col}")

                # Essayer de convertir avec plusieurs formats
                try:
                    # D'abord essayer la conversion standard
                    merged_df[col] = pd.to_datetime(merged_df[col], errors='coerce')

                    # Compter les valeurs non NaT après conversion standard
                    non_nat_after = merged_df[col].notna().sum()
                    logging.info(f"Après conversion standard: {non_nat_after} valeurs non NaT sur {len(merged_df)} lignes dans {col}")

                    # Si trop de NaT, essayer avec des formats spécifiques
                    if non_nat_after < non_null_before * 0.9:  # Si plus de 10% des valeurs non nulles sont devenues NaT
                        logging.warning(f"Perte de données dans {col}, essai avec formats spécifiques")

                        # Restaurer la colonne originale pour essayer d'autres formats
                        merged_df[col] = original_col

                        # Essayer avec différents formats courants
                        best_format = None
                        best_count = 0

                        for fmt in ['%d/%m/%Y %H:%M:%S', '%d/%m/%Y %H:%M', '%d-%m-%Y %H:%M:%S', '%Y-%m-%d %H:%M:%S', '%d/%m/%Y']:
                            try:
                                temp_col = pd.to_datetime(merged_df[col].astype(str), format=fmt, errors='coerce')
                                non_nat_count = temp_col.notna().sum()

                                if non_nat_count > best_count:
                                    best_count = non_nat_count
                                    best_format = fmt
                                    merged_df[col] = temp_col
                                    logging.info(f"Format {fmt} a converti {non_nat_count} valeurs pour {col}")
                            except Exception as e:
                                logging.debug(f"Erreur avec format {fmt}: {str(e)}")

                        if best_format:
                            logging.info(f"Meilleur format pour {col}: {best_format} avec {best_count} valeurs converties")
                        else:
                            # Si aucun format ne fonctionne mieux, restaurer les valeurs originales
                            merged_df[col] = original_col
                            logging.warning(f"Aucun format n'a amélioré la conversion pour {col}, restauration des valeurs originales")

                    # Vérifier les valeurs NaT après toutes les tentatives
                    final_nat_count = merged_df[col].isna().sum()
                    if final_nat_count > 0:
                        logging.warning(f"Après toutes les tentatives: {final_nat_count} valeurs NaT sur {len(merged_df)} lignes dans {col}")

                    # Afficher des statistiques après conversion
                    na_count = merged_df[col].isna().sum()
                    logging.info(f"Après conversion, {col} contient {na_count} valeurs NaT sur {len(merged_df)} lignes")
                    logging.info(f"Exemples de valeurs dans la colonne {col} après conversion: {merged_df[col].head(5).tolist()}")

                except Exception as e:
                    logging.error(f"Erreur lors de la conversion de {col}: {str(e)}")
            else:
                logging.warning(f"Colonne {col} non trouvée dans le dataframe")
        except Exception as e:
            logging.error(f"Erreur générale pour la colonne {col}: {str(e)}")
            logging.debug(traceback.format_exc())

    # Calculer le délai de résolution en jours décimaux (avec heures, minutes, secondes)
    delta = merged_df['Date résolution'] - merged_df['Date ouverture du cas']
    # Convertir les timedeltas en jours décimaux (1 jour = 86400 secondes)
    merged_df['Délai_résolution'] = delta.dt.total_seconds() / 86400
    # Arrondir à 2 décimales pour plus de lisibilité
    merged_df['Délai_résolution'] = merged_df['Délai_résolution'].round(2)

    # Conserver aussi le délai en jours entiers pour les conditions qui l'utilisent
    merged_df['Délai_résolution_jours'] = delta.dt.days

    # Nouvelles colonnes selon la spécification
    merged_df['Catégorie'] = ''
    merged_df['Facturation éligible'] = 'NON'

    # Conditions pour les catégories (sans ABS)
    conditions = [
        (merged_df['Type'] == 'SIGNALISATION') & (merged_df['Diagnostic_OC'].str.startswith('RET', na=False)),
        (merged_df['Type'] == 'SIGNALISATION') & (merged_df['Diagnostic_OC'].str.startswith('STT', na=False)),
        (merged_df['Type'] == 'EXPERTISE') & (merged_df['Diagnostic_OC'].str.startswith('RET', na=False)),
        (merged_df['Type'] == 'EXPERTISE') & (merged_df['Diagnostic_OC'].str.startswith('STT', na=False))
    ]
    categories = ['SIGRET', 'SIGSTT', 'EXPRET', 'EXPSTT']
    merged_df['Catégorie'] = np.select(conditions, categories, default='')

    # APPLICATION DES NOUVELLES RÈGLES DE RÉITÉRATION STT
    logging.info("Application des nouvelles règles de réitération STT...")
    print("⏳ Application des règles de réitération STT...")
    
    # Étape 1: Identifier tous les STT initialement éligibles
    stt_mask = merged_df['Diagnostic_OC'].str.startswith('STT', na=False)
    initial_stt_count = stt_mask.sum()
    merged_df.loc[stt_mask, 'Facturation éligible'] = 'OUI'
    
    logging.info(f"[STT] {initial_stt_count:,} STT identifiés initialement éligibles")
    print(f"[STT] {initial_stt_count:,} STT trouvés")
    
    # Étape 2: Appliquer la logique de réitération pour chaque Ref_MUT
    facturation_non_eligible_count = 0
    
    # Regrouper par OC et Ref_MUT - avec optimisation
    if 'Ref_MUT' in merged_df.columns and 'OC' in merged_df.columns:
        # Filtrer seulement les lignes avec Ref_MUT valide pour optimiser
        valid_ref_mut_mask = merged_df['Ref_MUT'].notna() & (merged_df['Ref_MUT'] != '')
        df_with_ref_mut = merged_df[valid_ref_mut_mask]
        
        grouped = df_with_ref_mut.groupby(['OC', 'Ref_MUT'])
        total_groups = len(grouped)
        
        logging.info(f"🔍 Analyse de {total_groups:,} groupes (OC, Ref_MUT) pour la réitération")
        print(f"🔍 Analyse de {total_groups:,} groupes pour la réitération...")
        
        processed_groups = 0
        
        for (oc, ref_mut), group in grouped:
            processed_groups += 1
            
            # Affichage du progrès tous les 1000 groupes
            if processed_groups % 1000 == 0 or processed_groups == total_groups:
                progress_pct = (processed_groups / total_groups) * 100
                print(f"⏳ Progression: {processed_groups:,}/{total_groups:,} groupes ({progress_pct:.1f}%)")
            
            # Trier par date d'ouverture (du plus ancien au plus récent)
            group_sorted = group.sort_values('Date ouverture du cas')
            
            # Identifier les déclenchements dans une fenêtre de 6 mois
            date_fin = group_sorted['Date résolution'].max()
            date_debut = date_fin - pd.Timedelta(days=180)  # 6 mois = 180 jours
            
            # Filtrer les déclenchements dans cette fenêtre
            group_period = group_sorted[
                (group_sorted['Date ouverture du cas'] >= date_debut) &
                (group_sorted['Date résolution'] <= date_fin)
            ]
            
            if len(group_period) > 1:  # S'il y a plusieurs déclenchements
                # Chercher le RET le plus récent
                ret_mask = group_period['Diagnostic_OC'].str.startswith('RET', na=False)
                if ret_mask.any():
                    # Trouver l'index du RET le plus récent
                    ret_tickets = group_period[ret_mask]
                    most_recent_ret_date = ret_tickets['Date ouverture du cas'].max()
                    
                    # Identifier tous les STT ANTÉRIEURS à ce RET
                    for idx, row in group_period.iterrows():
                        if (row['Diagnostic_OC'].startswith('STT') and 
                            row['Date ouverture du cas'] < most_recent_ret_date):
                            # Marquer ce STT comme non facturable
                            merged_df.loc[idx, 'Facturation éligible'] = 'NON'
                            facturation_non_eligible_count += 1
    
    logging.info(f"[OK] {facturation_non_eligible_count:,} STT rendus non éligibles à cause de la règle de réitération")
    print(f"[OK] Réitération terminée: {facturation_non_eligible_count:,} STT exclus")
    
    # Statistiques détaillées sur les STT
    stt_total = merged_df['Diagnostic_OC'].str.startswith('STT', na=False).sum()
    stt_eligible = ((merged_df['Diagnostic_OC'].str.startswith('STT', na=False)) & 
                   (merged_df['Facturation éligible'] == 'OUI')).sum()
    taux_exclusion = (facturation_non_eligible_count / stt_total * 100) if stt_total > 0 else 0
    
    logging.info(f"=== BILAN STT RÉITÉRATION ===")
    logging.info(f"Total STT: {stt_total:,}")
    logging.info(f"STT éligibles: {stt_eligible:,}")
    logging.info(f"STT exclus par réitération: {facturation_non_eligible_count:,}")
    logging.info(f"Taux d'exclusion: {taux_exclusion:.2f}%")
    print(f"📊 Taux d'exclusion réitération: {taux_exclusion:.1f}% ({facturation_non_eligible_count:,}/{stt_total:,} STT)")
    
    # Statistiques finales
    logging.info(f"Répartition des catégories: {merged_df['Catégorie'].value_counts().to_dict()}")
    logging.info(f"Nombre de cas éligibles: {(merged_df['Facturation éligible'] == 'OUI').sum()}")

    return merged_df

def reorganize_columns(df):
    """Réorganise les colonnes selon la nouvelle spécification exacte."""
    # Colonnes dans l'ordre EXACT spécifié
    exact_column_order = [
        'Statut', 'Identifiant cas', 'Ref OC', 'OC', 'Ref_MUT', 'Prise', 
        'Diagnostic en cours', 'Date ouverture du cas', 'Date résolution', 
        'Mois_Création', 'Mois_Résolution', 'Type', 'Source_OC', 'Famille_OC', 
        'Diagnostic_OC', 'Commentaire Résolution', 'TT STIT', 'PM', 
        'Région', 'STIT', 'DEPT', 'Propriétaire', 'Zone', 'Code OI InterOP', 
        'OI', 'Délai_résolution', 'Délai_résolution_jours', 'Catégorie', 
        'Facturation éligible'
    ]
    
    logging.info("=== RÉORGANISATION DES COLONNES ===")
    logging.info(f"Colonnes actuelles dans le DataFrame: {list(df.columns)}")
    
    # Créer un nouveau DataFrame avec exactement ces colonnes dans cet ordre
    df_final = pd.DataFrame()
    
    for col in exact_column_order:
        if col in df.columns:
            df_final[col] = df[col]
            logging.info(f"✓ Colonne '{col}' copiée depuis les données existantes")
        else:
            df_final[col] = ''
            logging.info(f"• Colonne '{col}' créée avec valeurs vides")
    
    # Vérifier que nous avons exactement les bonnes colonnes dans le bon ordre
    if list(df_final.columns) == exact_column_order:
        logging.info("[OK] Structure des colonnes EXACTEMENT conforme à la spécification")
    else:
        logging.error("[ERREUR] Problème dans la structure des colonnes!")
        
    logging.info(f"Structure finale: {list(df_final.columns)}")
    logging.info(f"Nombre de lignes conservées: {len(df_final)}")
    logging.info("=== FIN RÉORGANISATION ===")
    
    return df_final

def create_summary_sheet_for_group(df, ws, oc, selected_month, start_row=1):
    """Crée la feuille de synthèse pour un groupe OI/OC avec métriques STT.
       Prend en compte le mois sélectionné."""
    # Calculer les statistiques de base pour le groupe
    total_cas = len(df)
    cas_eligibles = (df['Facturation éligible'] == 'OUI').sum()
    pourcentage_eligibilite = (cas_eligibles / total_cas * 100) if total_cas > 0 else 0
    
    # Calcul des métriques STT ultra-sécurisé pour le groupe
    stt_total = 0
    stt_eligible = 0
    stt_non_eligible = 0
    taux_exclusion_reiteration = 0.0
    
    try:
        if 'Diagnostic_OC' in df.columns and not df['Diagnostic_OC'].empty:
            # Nettoyer et sécuriser les données Diagnostic_OC
            diagnostic_clean = df['Diagnostic_OC'].fillna('').astype(str)
            stt_mask = diagnostic_clean.str.startswith('STT', na=False)
            stt_total = int(stt_mask.sum())
            
            if stt_total > 0 and 'Facturation éligible' in df.columns:
                # Nettoyer et sécuriser les données Facturation éligible
                facturation_clean = df['Facturation éligible'].fillna('').astype(str)
                stt_non_eligible = int((stt_mask & (facturation_clean == 'NON')).sum())
                stt_eligible = int(stt_total - stt_non_eligible)
                
                # Calcul sécurisé du pourcentage
                if stt_total > 0:
                    taux_exclusion_reiteration = float(stt_non_eligible / stt_total * 100)
                else:
                    taux_exclusion_reiteration = 0.0
    except Exception as e:
        logging.warning(f"[WARNING] Erreur calcul STT groupe, valeurs par défaut utilisées: {str(e)}")
        stt_total = 0
        stt_eligible = 0
        stt_non_eligible = 0
        taux_exclusion_reiteration = 0.0
    
    # Stats ultra-sécurisées avec conversion de type
    stats = {
        "=== STATISTIQUES GENERALES ===": "",
        "Total des cas": int(total_cas),
        "Cas éligibles": int(cas_eligibles),
        "Pourcentage éligibilité": f"{float(pourcentage_eligibilite):.2f}%",
        "": "",
        "=== ANALYSE STT DETAILLEE ===": "",
        "Total STT": int(stt_total),
        "STT éligibles": int(stt_eligible),
        "STT exclus par réitération": int(stt_non_eligible),
        "Taux exclusion réitération": f"{float(taux_exclusion_reiteration):.2f}%",
        " ": "",
        "=== REPARTITION PAR CATEGORIE ===": "",
        "Répartition par catégorie": df['Catégorie'].value_counts().to_dict() if 'Catégorie' in df.columns else {}
    }

    # Ecriture du titre en A1
    title = f"Synthèse Facturation {oc} - Mois de résolution: {selected_month or 'Tous'}"  # Titre principal
    ws.cell(row=1, column=1, value=title)
    ws.cell(row=1, column=1).font = Font(bold=True, size=14)  # Gras et plus grand
    ws.cell(row=1, column=1).alignment = Alignment(horizontal='center')  # Centré
    ws.column_dimensions['A'].width = 90

   # Écrire les données dans la feuille de synthèse en A2 avec sécurisation
    for key, value in stats.items():
        try:
            # Sécuriser l'écriture des clés
            clean_key = str(key) if key is not None else ""
            ws.cell(row=start_row + 1, column=1, value=clean_key)
            
            if str(key).startswith("==="):
                # Formater les titres de section
                ws.cell(row=start_row + 1, column=1).font = Font(bold=True)
            elif isinstance(value, dict):
                # Écrire la répartition par catégorie avec sécurisation
                col = 2
                for cat, count in value.items():
                    try:
                        clean_cat = str(cat) if cat is not None else "Inconnu"
                        clean_count = int(count) if isinstance(count, (int, float)) else 0
                        ws.cell(row=start_row + 1, column=col, value=f"{clean_cat}: {clean_count}")
                        col += 1
                    except Exception:
                        ws.cell(row=start_row + 1, column=col, value="Erreur catégorie")
                        col += 1
                start_row += 1  # On passe à la ligne suivante
            elif key and not str(key).startswith("===") and value != "":
                # Sécuriser l'écriture des valeurs
                if isinstance(value, (int, float)):
                    clean_value = float(value) if isinstance(value, float) else int(value)
                    ws.cell(row=start_row + 1, column=2, value=clean_value)
                else:
                    clean_value = str(value) if value is not None else ""
                    ws.cell(row=start_row + 1, column=2, value=clean_value)
            
        except Exception as e:
            # En cas d'erreur, écrire une valeur par défaut
            logging.warning(f"[WARNING] Erreur écriture synthèse groupe ligne {start_row + 1}: {str(e)}")
            ws.cell(row=start_row + 1, column=1, value=f"Erreur: {str(key)}")
            
        start_row += 1 # Pour éviter l'écrasement on passe à la ligne suivante
    return start_row

def create_summary_sheet(result_path, df_final):
    """Crée une feuille de synthèse globale robuste et sans erreur."""
    try:
        wb = openpyxl.load_workbook(result_path)
        
        # Supprimer l'ancienne feuille si elle existe
        if 'Synthèse Globale' in wb.sheetnames:
            wb.remove(wb['Synthèse Globale'])
        
        # Créer une nouvelle feuille de synthèse propre
        ws_synth = wb.create_sheet(title='Synthèse Globale')

        # === CALCULS ROBUSTES (PAS DE FORMULES EXCEL) ===
        total_cas = len(df_final)
        cas_eligibles = (df_final['Facturation éligible'] == 'OUI').sum() if 'Facturation éligible' in df_final.columns else 0
        pourcentage_eligibilite = (cas_eligibles / total_cas * 100) if total_cas > 0 else 0
        
        # Calculs STT ultra-sécurisés avec conversion de type
        stt_total = 0
        stt_eligible = 0
        stt_non_eligible = 0
        taux_exclusion_reiteration = 0.0
        
        try:
            if 'Diagnostic_OC' in df_final.columns and not df_final['Diagnostic_OC'].empty:
                # Nettoyer et sécuriser les données Diagnostic_OC
                diagnostic_clean = df_final['Diagnostic_OC'].fillna('').astype(str)
                stt_mask = diagnostic_clean.str.startswith('STT', na=False)
                stt_total = int(stt_mask.sum())
                
                if stt_total > 0 and 'Facturation éligible' in df_final.columns:
                    # Nettoyer et sécuriser les données Facturation éligible
                    facturation_clean = df_final['Facturation éligible'].fillna('').astype(str)
                    stt_non_eligible = int((stt_mask & (facturation_clean == 'NON')).sum())
                    stt_eligible = int(stt_total - stt_non_eligible)
                    
                    # Calcul sécurisé du pourcentage
                    if stt_total > 0:
                        taux_exclusion_reiteration = float(stt_non_eligible / stt_total * 100)
                    else:
                        taux_exclusion_reiteration = 0.0
        except Exception as e:
            logging.warning(f"[WARNING] Erreur calcul STT, valeurs par défaut utilisées: {str(e)}")
            stt_total = 0
            stt_eligible = 0
            stt_non_eligible = 0
            taux_exclusion_reiteration = 0.0
        
        # === ÉCRITURE SÉCURISÉE DES DONNÉES ===
        row = 1
        
        # Titre principal
        ws_synth.cell(row=row, column=1, value="Synthèse Facturation Globale")
        ws_synth.cell(row=row, column=1).font = Font(bold=True, size=14)
        ws_synth.cell(row=row, column=1).alignment = Alignment(horizontal='center')
        row += 2
        
        # Statistiques principales avec métriques STT ultra-sécurisées
        stats_data = [
            ("=== STATISTIQUES GENERALES ===", ""),
            ("Total des cas", int(total_cas)),
            ("Cas éligibles", int(cas_eligibles)),
            ("Pourcentage éligibilité", f"{float(pourcentage_eligibilite):.2f}%"),
            ("", ""),
            ("=== ANALYSE STT DETAILLEE ===", ""),
            ("Total STT", int(stt_total)),
            ("STT éligibles", int(stt_eligible)),
            ("STT exclus par réitération", int(stt_non_eligible)),
            ("Taux exclusion réitération", f"{float(taux_exclusion_reiteration):.2f}%"),
        ]
        
        # Ajouter répartition par catégorie si disponible
        if 'Catégorie' in df_final.columns:
            stats_data.extend([
                ("", ""),
                ("=== REPARTITION PAR CATEGORIE ===", ""),
            ])
            try:
                cat_counts = df_final['Catégorie'].value_counts().to_dict()
                for cat, count in cat_counts.items():
                    stats_data.append((f"  {cat}", count))
            except Exception:
                stats_data.append(("Erreur calcul catégories", ""))
        
        # Écrire toutes les données avec validation
        for label, value in stats_data:
            try:
                # Sécuriser l'écriture des labels
                clean_label = str(label) if label is not None else ""
                ws_synth.cell(row=row, column=1, value=clean_label)
                
                if label and not str(label).startswith("===") and value != "":
                    # Sécuriser l'écriture des valeurs
                    if isinstance(value, (int, float)):
                        # S'assurer que les valeurs numériques sont bien converties
                        clean_value = float(value) if isinstance(value, float) else int(value)
                        ws_synth.cell(row=row, column=2, value=clean_value)
                    else:
                        # Pour les chaînes et autres types
                        clean_value = str(value) if value is not None else ""
                        ws_synth.cell(row=row, column=2, value=clean_value)
                        
                elif str(label).startswith("==="):
                    ws_synth.cell(row=row, column=1).font = Font(bold=True, color="0066CC")
                    
            except Exception as e:
                # En cas d'erreur, écrire une valeur par défaut
                logging.warning(f"[WARNING] Erreur écriture ligne {row}: {str(e)}")
                ws_synth.cell(row=row, column=1, value=f"Erreur: {str(label)}")
                
            row += 1

        # === SYNTHESE PAR OC ET OI (OPTIONNELLE) ===
        row += 1
        if all(col in df_final.columns for col in ['OI', 'OC']):
            ws_synth.cell(row=row, column=1, value="=== SYNTHESE PAR OC ET OI ===")
            ws_synth.cell(row=row, column=1).font = Font(bold=True, color="0066CC")
            row += 1
            
            # En-têtes du tableau
            headers = ["OI", "OC", "Total cas", "Cas éligibles", "% éligibilité"]
            for col_idx, header in enumerate(headers, 1):
                ws_synth.cell(row=row, column=col_idx, value=header)
                ws_synth.cell(row=row, column=col_idx).font = Font(bold=True)
            row += 1
            
            # Données par groupe OI/OC (sécurisé)
            try:
                grouped = df_final.groupby(['OI', 'OC'])
                for (oi, oc), group in grouped:
                    total_group = len(group)
                    eligible_group = (group['Facturation éligible'] == 'OUI').sum() if 'Facturation éligible' in group.columns else 0
                    percent_group = (eligible_group / total_group * 100) if total_group > 0 else 0
                    
                    ws_synth.cell(row=row, column=1, value=str(oi) if oi is not None else "N/A")
                    ws_synth.cell(row=row, column=2, value=str(oc) if oc is not None else "N/A")
                    ws_synth.cell(row=row, column=3, value=total_group)
                    ws_synth.cell(row=row, column=4, value=eligible_group)
                    ws_synth.cell(row=row, column=5, value=f"{percent_group:.2f}%")
                    row += 1
            except Exception as e:
                ws_synth.cell(row=row, column=1, value=f"Erreur calcul synthèse OI/OC: {str(e)}")
                row += 1

        # === FORMATAGE FINAL ROBUSTE ===
        # Ajuster les largeurs de colonnes
        ws_synth.column_dimensions['A'].width = 40
        ws_synth.column_dimensions['B'].width = 20
        for col in "CDEFGHIJ":
            ws_synth.column_dimensions[col].width = 15
        
        # Masquer les lignes de grille
        ws_synth.sheet_view.showGridLines = False
        
        # Sauvegarder le fichier
        wb.save(result_path)
        logging.info(f"[OK] Feuille de synthèse globale créée avec succès: {result_path}")

    except Exception as e:
        logging.error(f"[ERREUR] Création feuille de synthèse: {str(e)}")
        # En cas d'erreur, créer une feuille simple en fallback
        try:
            wb = openpyxl.load_workbook(result_path)
            if 'Synthèse Globale' in wb.sheetnames:
                wb.remove(wb['Synthèse Globale'])
            ws_simple = wb.create_sheet(title='Synthèse Globale')
            ws_simple.cell(row=1, column=1, value="Synthèse Facturation Globale")
            ws_simple.cell(row=1, column=1).font = Font(bold=True, size=14)
            ws_simple.cell(row=2, column=1, value=f"Total des cas: {len(df_final)}")
            if 'Facturation éligible' in df_final.columns:
                cas_eligibles = (df_final['Facturation éligible'] == 'OUI').sum()
                ws_simple.cell(row=3, column=1, value=f"Cas éligibles: {cas_eligibles}")
            wb.save(result_path)
            logging.info(f"[OK] Feuille de synthèse simplifiée créée en fallback")
        except Exception as e2:
            logging.error(f"[ERREUR] Impossible de créer même une feuille simple: {str(e2)}")

def split_final_file(result_path, output_dir, selected_month=None):
    """Découpe le fichier final par OI et OC et inclut la feuille de synthèse correspondante."""
    try:
        df = pd.read_excel(result_path, engine='openpyxl')
        # Chargement du workbook principal n'est pas nécessaire ici

        if selected_month:
            output_dir = output_dir / str(selected_month)
        output_dir.mkdir(parents=True, exist_ok=True)

        xpf_dir = output_dir / 'XPF'
        sfra_dir = output_dir / 'SFRA'
        xpf_dir.mkdir(parents=True, exist_ok=True)
        sfra_dir.mkdir(parents=True, exist_ok=True)

        grouped = df.groupby(['OI', 'OC'])

        for (oi, oc), group in grouped:
            target_dir = xpf_dir if oi == 'XPF' else sfra_dir if oi == 'SFRA' else None
            if not target_dir:
                continue

            group_clean = sanitize_excel_output(group)
            # Extraire le nom de base du fichier (peut contenir le mois de résolution)
            base_name = result_path.stem
            safe_oc = sanitize_filename(oc)
            safe_oi = sanitize_filename(oi)
            new_name = f"{base_name}_{safe_oc}_{safe_oi}.xlsx"
            new_path = target_dir / new_name

            # Création d'un nouveau workbook
            wb_new = openpyxl.Workbook()
            ws_new = wb_new.active
            ws_new.title = 'Data'

            # Convertir les colonnes de date en string si elles sont encore en datetime
            group_for_export = group_clean.copy()

            # Remplacer tous les NaN par des chaînes vides pour éviter d'avoir "nan" dans les fichiers découpés
            for col in group_for_export.columns:
                if group_for_export[col].dtype == 'object' or pd.api.types.is_string_dtype(group_for_export[col]):
                    group_for_export[col] = group_for_export[col].fillna('')

            # Traitement spécial pour les colonnes de date
            for col in ['Date ouverture du cas', 'Date résolution']:
                if col in group_for_export.columns:
                    # Vérifier si la colonne est de type datetime
                    if pd.api.types.is_datetime64_any_dtype(group_for_export[col]):
                        # Compter les valeurs NaT avant conversion
                        nat_count = group_for_export[col].isna().sum()
                        logging.info(f"Fichier découpé: Avant conversion en string, {col} contient {nat_count} valeurs NaT sur {len(group_for_export)} lignes")

                        # Convertir les dates valides au format JJ/MM/AAAA HH:MM:SS
                        group_for_export[col] = group_for_export[col].dt.strftime('%d/%m/%Y %H:%M:%S')

                        # Remplacer les NaN (qui étaient des NaT) par une chaîne vide
                        group_for_export[col] = group_for_export[col].fillna('')
                    else:
                        # Si la colonne n'est pas de type datetime, s'assurer qu'elle ne contient pas de valeurs nulles
                        group_for_export[col] = group_for_export[col].fillna('')

                    # Vérifier les valeurs vides après traitement
                    empty_count = (group_for_export[col] == '').sum()
                    logging.info(f"Fichier découpé: Après traitement, {col} contient {empty_count} valeurs vides sur {len(group_for_export)} lignes")

            # Traitement spécial pour Délai_résolution - remplacer les points par des virgules
            if 'Délai_résolution' in group_for_export.columns:
                # Convertir en string avec virgule au lieu de point
                group_for_export['Délai_résolution'] = group_for_export['Délai_résolution'].astype(str).str.replace('.', ',', regex=False)
                logging.info("Délai_résolution formaté avec virgules pour le fichier découpé")

            # Vérifier et corriger les incohérences entre Code OI InterOP et OI
            if 'Code OI InterOP' in group_for_export.columns and 'OI' in group_for_export.columns:
                # Cas spécifique : Code OI InterOP = SFRA doit avoir OI = SFRA
                sfra_mask = (group_for_export['Code OI InterOP'] == 'SFRA') & (group_for_export['OI'] != 'SFRA')
                sfra_count = sfra_mask.sum()

                if sfra_count > 0:
                    # Corriger les valeurs
                    group_for_export.loc[sfra_mask, 'OI'] = 'SFRA'
                    logging.info(f"Corrigé {sfra_count} lignes dans le fichier découpé pour que OI = SFRA lorsque Code OI InterOP = SFRA")

            # Vérification pour s'assurer qu'il ne reste que des tickets avec le statut "Clos"
            if 'Statut' in group_for_export.columns:
                # Nettoyer la colonne Statut (supprimer les espaces, normaliser la casse)
                group_for_export['Statut'] = group_for_export['Statut'].astype(str).str.strip().str.title()

                # Vérifier s'il reste des tickets avec un statut autre que "Clos"
                non_clos_mask = ~group_for_export['Statut'].str.contains('Clos', case=False, na=False)
                non_clos_count = non_clos_mask.sum()

                if non_clos_count > 0:
                    # Supprimer ces lignes
                    group_for_export = group_for_export[~non_clos_mask].copy()
                    logging.info(f"Fichier découpé: Supprimé {non_clos_count} lignes avec un statut autre que 'Clos'")

            # Traitement spécial pour DEPT - s'assurer qu'il est en format texte sans décimales et sans 'nan'
            if 'DEPT' in group_for_export.columns:
                # Vérifier les valeurs NaN avant le formatage
                nan_count = group_for_export['DEPT'].isna().sum()
                if nan_count > 0:
                    logging.warning(f"Trouvé {nan_count} valeurs NaN dans la colonne DEPT avant l'export du fichier découpé")

                # Remplacer les NaN par une chaîne vide
                group_for_export['DEPT'] = group_for_export['DEPT'].fillna('')

                # Convertir en string et supprimer les décimales (.0)
                group_for_export['DEPT'] = group_for_export['DEPT'].astype(str).replace(r'\.0$', '', regex=True)

                # Remplacer 'nan' par une chaîne vide
                group_for_export['DEPT'] = group_for_export['DEPT'].replace('nan', '')

                # Vérification finale et tentative de correction des DEPT vides
                dept_empty_count = (group_for_export['DEPT'] == '').sum()
                if dept_empty_count > 0:
                    logging.warning(f"Il reste {dept_empty_count} DEPT vides avant l'export du fichier découpé")

                    # Essayer de compléter les DEPT vides en utilisant le PM
                    pm_to_dept = {}
                    for idx, row in group_for_export.iterrows():
                        if row['PM'] and row['DEPT'] and row['DEPT'] != '':
                            pm_to_dept[row['PM']] = row['DEPT']

                    # Appliquer le dictionnaire pour compléter les valeurs manquantes
                    dept_filled = 0
                    for idx, row in group_for_export[group_for_export['DEPT'] == ''].iterrows():
                        if row['PM'] and row['PM'] in pm_to_dept:
                            group_for_export.at[idx, 'DEPT'] = pm_to_dept[row['PM']]
                            dept_filled += 1

                    if dept_filled > 0:
                        logging.info(f"DEPT complété pour {dept_filled} lignes supplémentaires dans le fichier découpé")

                logging.info("DEPT formaté sans décimales et sans 'nan' pour le fichier découpé")
                logging.info(f"Exemples de valeurs DEPT avant export du fichier découpé: {group_for_export['DEPT'].head(5).tolist()}")

            # Écriture des données avec traitement spécial pour les cellules vides
            # D'abord écrire les en-têtes cellule par cellule (SÉCURISÉ)
            headers = list(group_for_export.columns)
            for col_idx, header in enumerate(headers, 1):
                try:
                    clean_header = str(header) if header is not None else f"Col_{col_idx}"
                    ws_new.cell(row=1, column=col_idx, value=clean_header)
                except Exception as e:
                    logging.warning(f"[WARNING] Erreur écriture en-tête découpage col {col_idx}: {str(e)}")
                    ws_new.cell(row=1, column=col_idx, value=f"Col_{col_idx}")

            # Ensuite écrire les données ligne par ligne, cellule par cellule (SÉCURISÉ)
            for row_idx, (_, row) in enumerate(group_for_export.iterrows(), 2):  # Commencer à la ligne 2
                for col_idx, col_name in enumerate(headers, 1):
                    try:
                        # Récupérer et nettoyer la valeur
                        raw_value = row[col_name]
                        
                        if pd.isna(raw_value) or raw_value == 'nan' or raw_value == '':
                            clean_value = ""  # Cellule vide
                        elif isinstance(raw_value, (int, float)):
                            # S'assurer que les nombres sont bien formatés
                            if pd.isna(raw_value):
                                clean_value = ""
                            else:
                                clean_value = float(raw_value) if isinstance(raw_value, float) else int(raw_value)
                        else:
                            # Convertir en string sécurisé
                            clean_value = str(raw_value) if raw_value is not None else ""
                        
                        ws_new.cell(row=row_idx, column=col_idx, value=clean_value)
                        
                    except Exception as e:
                        logging.warning(f"[WARNING] Erreur écriture découpage ligne {row_idx}, col {col_idx}: {str(e)}")
                        ws_new.cell(row=row_idx, column=col_idx, value="")

            # Formatage :
            date_columns = []
            # Identifier les colonnes de date par leur nom (SÉCURISÉ)
            date_columns = []
            try:
                for col_idx in range(1, len(headers) + 1):
                    cell_value = ws_new.cell(row=1, column=col_idx).value
                    if cell_value and str(cell_value) in ['Date ouverture du cas', 'Date résolution']:
                        date_columns.append(col_idx)
            except Exception as e:
                logging.warning(f"[WARNING] Erreur identification colonnes de date découpage: {str(e)}")
                date_columns = []

            # Identifier les colonnes spéciales pour le formatage (SÉCURISÉ)
            delai_column = None
            dept_column = None
            try:
                for col_idx in range(1, len(headers) + 1):
                    cell_value = ws_new.cell(row=1, column=col_idx).value
                    if cell_value == 'Délai_résolution':
                        delai_column = col_idx
                    elif cell_value == 'DEPT':
                        dept_column = col_idx
            except Exception as e:
                logging.warning(f"[WARNING] Erreur identification colonnes spéciales découpage: {str(e)}")

            # Appliquer le formatage approprié à chaque cellule
            for row in ws_new.iter_rows(min_row=2):  # Commencer à la ligne 2 (après les en-têtes)
                for i, cell in enumerate(row, 1):  # i commence à 1 pour correspondre aux indices de colonnes Excel
                    if i in date_columns:
                        # Format de date français pour les colonnes de date
                        if cell.value and str(cell.value).strip():
                            cell.number_format = 'dd/mm/yyyy hh:mm:ss'
                    elif i == delai_column:
                        # Format décimal pour la colonne Délai_résolution avec virgule
                        if cell.value is not None:
                            # Appliquer un format texte pour conserver la virgule
                            cell.number_format = '@'
                    elif i == dept_column:
                        # Format texte pour la colonne DEPT
                        cell.number_format = '@'
                    else:
                        # Format texte pour les autres colonnes
                        cell.number_format = '@'

            # Ajuster la largeur des colonnes
            for i, column in enumerate(ws_new.columns):
                max_length = 0
                column_letter = openpyxl.utils.get_column_letter(i + 1)
                for cell in column:
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                adjusted_width = (max_length + 2) * 1.2
                ws_new.column_dimensions[column_letter].width = min(adjusted_width, 50)  # Limiter à 50 pour éviter des colonnes trop larges

            ws_new.sheet_view.showGridLines = False

            # Création de la feuille de synthèse par OI/OC
            sheet_name = 'Synthèse Facturation'
            ws_synth = wb_new.create_sheet(title=sheet_name)  # Créer la feuille dans chaque fichier

            # Calcul et écriture des statistiques pour ce groupe
            row_start = 1
            row_start = create_summary_sheet_for_group(group, ws_synth, oc, selected_month, row_start)  # On passe OC et selected_month

            # Réglage de la largeur des colonnes
            for col in "A":  # Colonnes à ajuster
                ws_synth.column_dimensions[col].width = 80

            for col in "BCDE":  # Colonnes à ajuster
                ws_synth.column_dimensions[col].width = 30  # Ajuste la largeur


            # Formatage de la feuille de synthèse
            for row in ws_synth.iter_rows():
                for cell in row:
                    cell.number_format = '@'
            ws_synth.sheet_view.showGridLines = False

            # Sauvegarde
            wb_new.save(new_path)
            logging.info(f"Fichier créé : {new_path}")
        logging.info("Découpage terminé avec succès.")
        return True

    except Exception as e:
        logging.error(f"ERREUR découpage : {str(e)}")
        logging.debug(traceback.format_exc())
        return False

def get_selected_month_from_reporting(df_reporting):
    """Permet à l'utilisateur de sélectionner un mois spécifique à partir des données du reporting déjà chargées."""
    try:
        logging.info("Analyse des mois disponibles dans les donnees reporting...")
        print("Analyse des mois de resolution disponibles...")
        
        # Vérifier si la colonne 'Date résolution' existe
        if 'Date résolution' not in df_reporting.columns:
            logging.warning("Colonne 'Date resolution' non trouvee. Impossible de filtrer par mois.")
            print("ERREUR: Colonne 'Date resolution' non trouvee.")
            return None

        # Faire une copie pour éviter de modifier l'original
        df_temp = df_reporting.copy()
        
        # Convertir les dates (les données sont en string suite au chargement optimisé)
        print("Conversion des dates...")
        df_temp['Date résolution'] = pd.to_datetime(df_temp['Date résolution'], errors='coerce', dayfirst=True)

        # Créer la colonne Mois_Résolution au format YYYY_MM
        df_temp['Mois_Résolution'] = df_temp['Date résolution'].dt.strftime('%Y_%m')
        # Nettoyer les espaces insécables qui peuvent être présents
        df_temp['Mois_Résolution'] = df_temp['Mois_Résolution'].str.replace('\xa0', '', regex=False)

        # Obtenir les mois uniques et les trier
        unique_months = df_temp['Mois_Résolution'].dropna().unique()
        unique_months = sorted(unique_months, reverse=True)  # Plus récent en premier

        if len(unique_months) == 0:
            logging.warning("Aucun mois valide trouve dans les donnees.")
            print("ERREUR: Aucun mois valide trouve.")
            return None

        print(f"\n{len(unique_months)} mois disponibles trouves")
        print("\n=== SELECTION DU MOIS DE RESOLUTION ===")
        print("Mois disponibles dans les donnees :")
        
        # Afficher les mois avec compteurs
        month_counts = {}
        for month in unique_months:
            count = (df_temp['Mois_Résolution'] == month).sum()
            month_counts[month] = count
        
        for i, month in enumerate(unique_months, 1):
            # Convertir le format YYYY_MM en format lisible
            try:
                year, month_num = month.split('_')
                month_name = pd.to_datetime(f"{year}-{month_num}-01").strftime('%B %Y')
                count = month_counts[month]
                print(f"{i:2d}. {month_name} ({month}) - {count:,} cas")
            except:
                print(f"{i:2d}. {month} - {month_counts[month]:,} cas")

        print(f"{len(unique_months)+1:2d}. Tous les mois (traitement complet)")

        while True:
            try:
                choice = input(f"\nVotre choix (1-{len(unique_months)+1}): ").strip()
                if choice.isdigit():
                    choice_int = int(choice)
                    if 1 <= choice_int <= len(unique_months):
                        selected = unique_months[choice_int - 1]
                        try:
                            year, month_num = selected.split('_')
                            month_name = pd.to_datetime(f"{year}-{month_num}-01").strftime('%B %Y')
                            count = month_counts[selected]
                            print(f"Mois selectionne: {month_name} ({selected}) - {count:,} cas a traiter")
                        except:
                            print(f"Mois selectionne: {selected} - {month_counts[selected]:,} cas a traiter")
                        return selected
                    elif choice_int == len(unique_months) + 1:
                        total_cases = len(df_temp)
                        print(f"Tous les mois selectionnes - {total_cases:,} cas a traiter")
                        return None
                    else:
                        print(f"Choix invalide. Saisissez un nombre entre 1 et {len(unique_months)+1}.")
                else:
                    print("Veuillez saisir un nombre.")
            except ValueError:
                print("Entree invalide. Veuillez saisir un nombre.")
            except KeyboardInterrupt:
                print("\nOperation annulee.")
                return None

    except Exception as e:
        logging.error(f"Erreur lors de la selection du mois : {e}")
        print(f"Erreur lors de la selection du mois : {e}")
        return None

def get_user_date():
    """Permet à l'utilisateur de choisir la date pour créer le dossier."""
    while True:
        try:
            print("\n=== CHOIX DE LA DATE POUR LE DOSSIER ===")
            print("ATTENTION: Ceci est la date du DOSSIER de sauvegarde, pas le mois de resolution!")
            print("Le mois de resolution sera choisi apres le chargement des donnees.")
            print("\n1. Utiliser la date d'aujourd'hui")
            print("2. Saisir une date personnalisee (format YYYY_MM_DD)")
            choice = input("Votre choix (1 ou 2): ").strip()
            
            if choice == "1":
                return datetime.date.today().strftime("%Y_%m_%d")
            elif choice == "2":
                date_input = input("Saisissez la date (format YYYY_MM_DD): ").strip()
                # Vérifier le format
                try:
                    datetime.datetime.strptime(date_input, "%Y_%m_%d")
                    return date_input
                except ValueError:
                    print("Format incorrect. Utilisez le format YYYY_MM_DD (ex: 2025_08_04)")
                    continue
            else:
                print("Choix invalide. Saisissez 1 ou 2.")
                continue
        except KeyboardInterrupt:
            print("\nOpération annulée.")
            exit(0)
        except Exception as e:
            print(f"Erreur: {e}")
            continue

def main():
    base_path = Path(r"C:\Users\<USER>\Desktop\KPI")
    
    # Permettre à l'utilisateur de choisir la date
    today_str = get_user_date()
    print(f"Date sélectionnée: {today_str}")
    
    output_dir = base_path / "Facturation_STT" / today_str
    ensure_directory_exists(output_dir)
    log_file = output_dir / "kpi_processing.log"
    setup_logging(log_file)
    logging.info(f"Début traitement - {today_str}")
    
    # Nouvelles sources selon les spécifications
    src_files = {
        "extract": Path(r"D:\donnees\FTTH\export\300_FTTH_extract3.0.xlsx"),
        "reporting": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\FTTH - Reporting cas STC_REFMUT.xlsx"),
        "stit": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\_FTTH_TT_STIT.xlsx"),
        "analyse_manquant": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\Analyse_STIT_manquant.xlsx"),
        "pm_sadira": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\FTTH_Liste_PM_source_sadira.xlsx"),
        "liste_oi": Path(r"U:\Lyon\STC Rési Bron\ISABEL\FTTH\Data_FTTH\Liste_OI.xlsx"),
        "update_ref_mut": Path(r"\\somtous\somtous\BITOOLV4\ECHANGE\STC_OI\FTTH\update_Base_manquant_REF_MUT.xlsx")
    }

    # Modifier copy_file pour ne copier que si le fichier source est plus récent que la destination
    try:
        dest_files = {key: output_dir / src_files[key].name for key in src_files}
        # Avant de copier, on vérifie la date de modification du fichier source et de la destination
        for key in src_files:
            # Vérifier si le fichier source existe avant de tenter la copie
            if src_files[key].exists():
                copy_file(src_files[key], dest_files[key])
            else:
                logging.warning(f"Fichier source non trouvé: {src_files[key]}")
                if key in ["analyse_manquant", "pm_sadira", "liste_oi", "update_ref_mut"]:
                    logging.info(f"Le fichier {src_files[key].name} est optionnel, continuons sans lui")
                else:
                    logging.error(f"Fichier requis manquant: {src_files[key]}")
                    raise FileNotFoundError(f"Fichier requis non trouvé: {src_files[key]}")

        # Fusion des données avec optimisations
        print("\n=== FUSION DES FICHIERS ===")
        df_merged, df_reporting_original = merge_datasets(
            dest_files["extract"],
            dest_files["reporting"],
            dest_files["stit"],
            dest_files["analyse_manquant"],
            dest_files["pm_sadira"],
            dest_files["liste_oi"],
            dest_files["update_ref_mut"]
        )

        # Demander à l'utilisateur de choisir le mois basé sur les données chargées
        print("\n=== SELECTION DU MOIS DE RESOLUTION ===")
        print("IMPORTANT: Vous allez maintenant choisir le MOIS DE RESOLUTION pour la facturation")
        selected_month = get_selected_month_from_reporting(df_reporting_original)

        # Appliquer le filtre Mois_Résolution avant le calcul
        print("\n=== APPLICATION DES REGLES DE FACTURATION ===")
        df_final = determine_facturation_eligibility(df_merged, selected_month)

        # Réorganiser les colonnes selon la nouvelle spécification
        print("\n=== REORGANISATION DES COLONNES ===")
        df_final = reorganize_columns(df_final)

        # Simplification : plus besoin de compléter les données manquantes
        logging.info("Traitement simplifié - pas de complétion des données manquantes")

        # Vérifier les colonnes de date avant l'export
        for col in ['Date ouverture du cas', 'Date résolution']:
            if col in df_final.columns:
                # Afficher des exemples de valeurs
                logging.info(f"Exemples de valeurs dans {col} avant export: {df_final[col].head(5).tolist()}")
                # Compter les valeurs vides
                empty_count = (df_final[col].isna() | (df_final[col] == '')).sum()
                logging.info(f"Colonne {col}: {empty_count} valeurs vides sur {len(df_final)} lignes")

        # Créer le nom du fichier en incluant le mois et l'année de résolution si un mois est sélectionné
        if selected_month:
            # Le format du mois est généralement YYYY_MM
            file_name = f"FTTH_Reporting_Final_{selected_month}.xlsx"
            logging.info(f"Nom du fichier avec mois de résolution: {file_name}")
        else:
            file_name = "FTTH_Reporting_Final.xlsx"

        result_path = output_dir / file_name
        
        # VÉRIFICATION FINALE DE LA STRUCTURE AVANT EXPORT
        logging.info("=== VÉRIFICATION FINALE AVANT EXPORT ===")
        expected_final_structure = [
            'Statut', 'Identifiant cas', 'Ref OC', 'OC', 'Ref_MUT', 'Prise', 
            'Diagnostic en cours', 'Date ouverture du cas', 'Date résolution', 
            'Mois_Création', 'Mois_Résolution', 'Type', 'Source_OC', 'Famille_OC', 
            'Diagnostic_OC', 'Commentaire Résolution', 'TT STIT', 'PM', 
            'Région', 'STIT', 'DEPT', 'Propriétaire', 'Zone', 'Code OI InterOP', 
            'OI', 'Délai_résolution', 'Délai_résolution_jours', 'Catégorie', 
            'Facturation éligible'
        ]
        
        actual_structure = list(df_final.columns)
        
        if actual_structure == expected_final_structure:
            logging.info("[OK] STRUCTURE FINALE PARFAITEMENT CONFORME!")
            logging.info(f"[FINAL] Fichier final: {len(df_final)} lignes x {len(actual_structure)} colonnes")
        else:
            logging.error("[ERREUR] STRUCTURE FINALE NON CONFORME!")
            logging.error(f"Attendue: {expected_final_structure}")
            logging.error(f"Actuelle: {actual_structure}")
            
            # Identifier les différences
            missing = [col for col in expected_final_structure if col not in actual_structure]
            extra = [col for col in actual_structure if col not in expected_final_structure]
            if missing:
                logging.error(f"Colonnes manquantes: {missing}")
            if extra:
                logging.error(f"Colonnes en trop: {extra}")
        
        logging.info("=== FIN VÉRIFICATION ===")
        
        print(f"\n💾 EXPORT EXCEL")
        print(f"📁 Création du fichier: {file_name}")
        print(f"[FINAL] {len(df_final):,} lignes × {len(df_final.columns)} colonnes")
        
        with pd.ExcelWriter(result_path, engine='openpyxl') as writer:
            # Remplacer tous les NaN par des chaînes vides pour éviter d'avoir "nan" dans le fichier final
            for col in df_final.columns:
                if df_final[col].dtype == 'object' or pd.api.types.is_string_dtype(df_final[col]):
                    df_final[col] = df_final[col].fillna('')

            # Traitement spécial pour les colonnes de date
            for col in ['Date ouverture du cas', 'Date résolution']:
                if col in df_final.columns:
                    # Vérifier si la colonne est de type datetime
                    if pd.api.types.is_datetime64_any_dtype(df_final[col]):
                        # Compter les valeurs NaT avant conversion
                        nat_count = df_final[col].isna().sum()
                        logging.info(f"Avant conversion en string, {col} contient {nat_count} valeurs NaT sur {len(df_final)} lignes")

                        # Convertir les dates valides au format JJ/MM/AAAA HH:MM:SS
                        df_final[col] = df_final[col].dt.strftime('%d/%m/%Y %H:%M:%S')

                        # Remplacer les NaN (qui étaient des NaT) par une chaîne vide
                        df_final[col] = df_final[col].fillna('')
                    else:
                        # Si la colonne n'est pas de type datetime, s'assurer qu'elle ne contient pas de valeurs nulles
                        df_final[col] = df_final[col].fillna('')

                    # Vérifier les valeurs vides après traitement
                    empty_count = (df_final[col] == '').sum()
                    logging.info(f"Après traitement, {col} contient {empty_count} valeurs vides sur {len(df_final)} lignes")

            # Traitement spécial pour Délai_résolution - remplacer les points par des virgules
            if 'Délai_résolution' in df_final.columns:
                # Convertir en string avec virgule au lieu de point
                df_final['Délai_résolution'] = df_final['Délai_résolution'].astype(str).str.replace('.', ',', regex=False)
                logging.info("Délai_résolution formaté avec virgules pour le fichier principal")

            # Vérification finale des incohérences entre Code OI InterOP et OI
            if 'Code OI InterOP' in df_final.columns and 'OI' in df_final.columns:
                # Cas spécifique : Code OI InterOP = SFRA doit avoir OI = SFRA
                sfra_mask = (df_final['Code OI InterOP'] == 'SFRA') & (df_final['OI'] != 'SFRA')
                sfra_count = sfra_mask.sum()

                if sfra_count > 0:
                    # Afficher les valeurs incohérentes pour débogage
                    logging.warning(f"Vérification finale: Trouvé {sfra_count} lignes avec Code OI InterOP = SFRA mais OI != SFRA")

                    # Corriger les valeurs
                    df_final.loc[sfra_mask, 'OI'] = 'SFRA'
                    logging.info(f"Vérification finale: Corrigé {sfra_count} lignes pour que OI = SFRA")

            # Vérification finale pour s'assurer qu'il ne reste que des tickets avec le statut "Clos"
            if 'Statut' in df_final.columns:
                # Nettoyer la colonne Statut (supprimer les espaces, normaliser la casse)
                df_final['Statut'] = df_final['Statut'].astype(str).str.strip().str.title()

                # Vérifier s'il reste des tickets avec un statut autre que "Clos"
                non_clos_mask = ~df_final['Statut'].str.contains('Clos', case=False, na=False)
                non_clos_count = non_clos_mask.sum()

                if non_clos_count > 0:
                    # Afficher les statuts non-Clos restants
                    non_clos_statuses = df_final.loc[non_clos_mask, 'Statut'].unique()
                    logging.warning(f"Vérification finale: Trouvé {non_clos_count} lignes avec un statut autre que 'Clos': {non_clos_statuses}")

                    # Supprimer ces lignes
                    df_final = df_final[~non_clos_mask].copy()
                    logging.info(f"Vérification finale: Supprimé {non_clos_count} lignes avec un statut autre que 'Clos'")
                    logging.info(f"Nombre final de lignes: {len(df_final)}")

            # Traitement spécial pour DEPT - s'assurer qu'il est en format texte sans décimales et sans 'nan'
            if 'DEPT' in df_final.columns:
                # Vérifier les valeurs NaN avant le formatage
                nan_count = df_final['DEPT'].isna().sum()
                if nan_count > 0:
                    logging.warning(f"Trouvé {nan_count} valeurs NaN dans la colonne DEPT avant l'export")

                # Remplacer les NaN par une chaîne vide
                df_final['DEPT'] = df_final['DEPT'].fillna('')

                # Convertir en string et supprimer les décimales (.0)
                df_final['DEPT'] = df_final['DEPT'].astype(str).replace(r'\.0$', '', regex=True)

                # Remplacer 'nan' par une chaîne vide
                df_final['DEPT'] = df_final['DEPT'].replace('nan', '')

                # Vérification finale et tentative de correction des DEPT vides
                dept_empty_count = (df_final['DEPT'] == '').sum()
                if dept_empty_count > 0:
                    logging.warning(f"Il reste {dept_empty_count} DEPT vides avant l'export final")

                    # Essayer de compléter les DEPT vides en utilisant le PM
                    pm_to_dept = {}
                    for idx, row in df_final.iterrows():
                        if row['PM'] and row['DEPT'] and row['DEPT'] != '':
                            pm_to_dept[row['PM']] = row['DEPT']

                    # Appliquer le dictionnaire pour compléter les valeurs manquantes
                    dept_filled = 0
                    for idx, row in df_final[df_final['DEPT'] == ''].iterrows():
                        if row['PM'] and row['PM'] in pm_to_dept:
                            df_final.at[idx, 'DEPT'] = pm_to_dept[row['PM']]
                            dept_filled += 1

                    if dept_filled > 0:
                        logging.info(f"DEPT complété pour {dept_filled} lignes supplémentaires en utilisant les correspondances PM-DEPT")

                logging.info("DEPT formaté sans décimales et sans 'nan' pour le fichier principal")
                logging.info(f"Exemples de valeurs DEPT avant export: {df_final['DEPT'].head(10).tolist()}")

            # Exporter vers Excel avec traitement spécial pour les cellules vides
            # Créer la feuille
            writer.book.create_sheet('Rapport')
            worksheet = writer.book['Rapport']

            # Écrire les en-têtes cellule par cellule (SÉCURISÉ)
            headers = list(df_final.columns)
            for col_idx, header in enumerate(headers, 1):
                try:
                    clean_header = str(header) if header is not None else f"Col_{col_idx}"
                    worksheet.cell(row=1, column=col_idx, value=clean_header)
                except Exception as e:
                    logging.warning(f"[WARNING] Erreur écriture en-tête col {col_idx}: {str(e)}")
                    worksheet.cell(row=1, column=col_idx, value=f"Col_{col_idx}")

            # Écrire les données ligne par ligne, cellule par cellule (SÉCURISÉ)
            for row_idx, (_, row) in enumerate(df_final.iterrows(), 2):  # Commencer à la ligne 2
                for col_idx, col_name in enumerate(headers, 1):
                    try:
                        # Récupérer et nettoyer la valeur
                        raw_value = row[col_name]
                        
                        if pd.isna(raw_value) or raw_value == 'nan' or raw_value == '':
                            clean_value = ""  # Cellule vide
                        elif isinstance(raw_value, (int, float)):
                            # S'assurer que les nombres sont bien formatés
                            if pd.isna(raw_value):
                                clean_value = ""
                            else:
                                clean_value = float(raw_value) if isinstance(raw_value, float) else int(raw_value)
                        else:
                            # Convertir en string sécurisé
                            clean_value = str(raw_value) if raw_value is not None else ""
                        
                        worksheet.cell(row=row_idx, column=col_idx, value=clean_value)
                        
                    except Exception as e:
                        logging.warning(f"[WARNING] Erreur écriture ligne {row_idx}, col {col_idx}: {str(e)}")
                        worksheet.cell(row=row_idx, column=col_idx, value="")

            # Identifier les colonnes de date par leur nom (SÉCURISÉ)
            date_columns = []
            try:
                for col_idx in range(1, len(headers) + 1):
                    cell_value = worksheet.cell(row=1, column=col_idx).value
                    if cell_value and str(cell_value) in ['Date ouverture du cas', 'Date résolution']:
                        date_columns.append(col_idx)
            except Exception as e:
                logging.warning(f"[WARNING] Erreur identification colonnes de date principale: {str(e)}")
                date_columns = []

            # Identifier les colonnes spéciales pour le formatage (SÉCURISÉ)
            delai_column = None
            dept_column = None
            try:
                for col_idx in range(1, len(headers) + 1):
                    cell_value = worksheet.cell(row=1, column=col_idx).value
                    if cell_value == 'Délai_résolution':
                        delai_column = col_idx
                    elif cell_value == 'DEPT':
                        dept_column = col_idx
            except Exception as e:
                logging.warning(f"[WARNING] Erreur identification colonnes spéciales principale: {str(e)}")

            # Appliquer le formatage approprié à chaque cellule
            for row in worksheet.iter_rows(min_row=2):  # Commencer à la ligne 2 (après les en-têtes)
                for i, cell in enumerate(row, 1):  # i commence à 1 pour correspondre aux indices de colonnes Excel
                    if i in date_columns:
                        # Format de date français pour les colonnes de date
                        if cell.value and str(cell.value).strip():
                            cell.number_format = 'dd/mm/yyyy hh:mm:ss'
                    elif i == delai_column:
                        # Format décimal pour la colonne Délai_résolution avec virgule
                        if cell.value is not None:
                            # Appliquer un format texte pour conserver la virgule
                            cell.number_format = '@'
                    elif i == dept_column:
                        # Format texte pour la colonne DEPT
                        cell.number_format = '@'
                    else:
                        # Format texte pour les autres colonnes
                        cell.number_format = '@'

            # Ajuster la largeur des colonnes
            for i, column in enumerate(worksheet.columns):
                max_length = 0
                column_letter = openpyxl.utils.get_column_letter(i + 1)
                for cell in column:
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                adjusted_width = (max_length + 2) * 1.2
                worksheet.column_dimensions[column_letter].width = min(adjusted_width, 50)  # Limiter à 50 pour éviter des colonnes trop larges

            worksheet.sheet_view.showGridLines = False

        create_summary_sheet(result_path, df_final)

        # Déplacer le fichier dans le dossier du mois de résolution si un mois est sélectionné
        if selected_month:
            # Créer le chemin du dossier du mois
            month_dir = output_dir / str(selected_month)
            month_dir.mkdir(parents=True, exist_ok=True)

            # Chemin de destination pour le fichier (utilise le même nom que result_path)
            dest_path = month_dir / result_path.name

            try:
                # Vérifier si le fichier existe déjà à destination et le supprimer si nécessaire
                if dest_path.exists():
                    dest_path.unlink()

                # Copier le fichier vers le dossier du mois
                shutil.copy2(result_path, dest_path)
                logging.info(f"Fichier {result_path.name} copié dans le dossier du mois {selected_month}")

                # Utiliser le nouveau chemin pour la découpe
                split_final_file(dest_path, output_dir, selected_month)
            except Exception as e:
                logging.error(f"Erreur lors du déplacement du fichier vers le dossier du mois: {str(e)}")
                # En cas d'erreur, continuer avec le fichier original
                split_final_file(result_path, output_dir, selected_month)
        else:
            # Si aucun mois n'est sélectionné, procéder normalement
            split_final_file(result_path, output_dir, selected_month)

        # CONFIRMATION FINALE DE LA STRUCTURE
        logging.info("=== CONFIRMATION FINALE ===")
        logging.info("[OK] TRAITEMENT COMPLET RÉUSSI !")
        logging.info(f"📁 Fichier Excel créé: {result_path.name}")
        logging.info(f"[FINAL] Structure finale: {len(df_final)} lignes × 29 colonnes")
        logging.info("📋 Colonnes dans l'ordre exact spécifié:")
        for i, col in enumerate(df_final.columns, 1):
            logging.info(f"   {i:2d}. {col}")
        logging.info("=== FIN TRAITEMENT ===")
        return 0

    except Exception as e:
        logging.error(f"ERREUR GLOBALE : {str(e)}")
        logging.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    mp.set_start_method('spawn', force=True)
    main()
