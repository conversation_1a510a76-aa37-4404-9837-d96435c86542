#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Correctif complet pour tous les problèmes Excel FTTH
- Corrige les erreurs de formules dans toutes les feuilles
- Ajoute les métriques STT aux petits fichiers (Synthèse Facturation)
"""

import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
import pandas as pd
import logging
from pathlib import Path
import glob

def calculate_stt_metrics(df):
    """Calcule les métriques STT pour un DataFrame"""
    stt_total = 0
    stt_eligible = 0
    stt_non_eligible = 0
    taux_exclusion_reiteration = 0
    
    if 'Diagnostic_OC' in df.columns and 'Facturation éligible' in df.columns:
        # Compter les STT
        stt_mask = df['Diagnostic_OC'].str.startswith('STT', na=False)
        stt_total = stt_mask.sum()
        
        if stt_total > 0:
            # STT non éligibles (exclus par réitération)
            stt_non_eligible = (stt_mask & (df['Facturation éligible'] == 'NON')).sum()
            # STT éligibles
            stt_eligible = stt_total - stt_non_eligible
            # Taux d'exclusion
            taux_exclusion_reiteration = (stt_non_eligible / stt_total * 100)
    
    return stt_total, stt_eligible, stt_non_eligible, taux_exclusion_reiteration

def fix_synthesis_sheet(ws, df, title_suffix=""):
    """Crée ou corrige une feuille de synthèse avec métriques STT"""
    # Effacer le contenu existant
    ws.delete_rows(1, ws.max_row)
    ws.delete_cols(1, ws.max_column)
    
    # Calculs de base
    total_cas = len(df)
    cas_eligibles = (df['Facturation éligible'] == 'OUI').sum() if 'Facturation éligible' in df.columns else 0
    pourcentage_eligibilite = (cas_eligibles / total_cas * 100) if total_cas > 0 else 0
    
    # Métriques STT
    stt_total, stt_eligible, stt_non_eligible, taux_exclusion_reiteration = calculate_stt_metrics(df)
    
    # Titre principal
    row = 1
    title = f"Synthèse Facturation{title_suffix}"
    ws.cell(row=row, column=1, value=title)
    ws.cell(row=row, column=1).font = Font(bold=True, size=14)
    ws.cell(row=row, column=1).alignment = Alignment(horizontal='center')
    row += 2
    
    # Données de synthèse avec métriques STT
    stats_data = [
        ("=== STATISTIQUES GENERALES ===", ""),
        ("Total des cas", total_cas),
        ("Cas éligibles", cas_eligibles),
        ("Pourcentage éligibilité", f"{pourcentage_eligibilite:.2f}%"),
        ("", ""),
        ("=== ANALYSE STT DETAILLEE ===", ""),
        ("Total STT", stt_total),
        ("STT éligibles", stt_eligible),
        ("STT exclus par réitération", stt_non_eligible),
        ("Taux exclusion réitération", f"{taux_exclusion_reiteration:.2f}%"),
    ]
    
    # Ajouter répartition par catégorie si disponible
    if 'Catégorie' in df.columns:
        stats_data.extend([
            ("", ""),
            ("=== REPARTITION PAR CATEGORIE ===", ""),
        ])
        cat_counts = df['Catégorie'].value_counts().to_dict()
        for cat, count in cat_counts.items():
            stats_data.append((f"  {cat}", count))
    
    # Écrire les données
    for label, value in stats_data:
        ws.cell(row=row, column=1, value=label)
        if label and not label.startswith("===") and value != "":
            ws.cell(row=row, column=2, value=value)
        elif label.startswith("==="):
            ws.cell(row=row, column=1).font = Font(bold=True, color="0066CC")
        row += 1
    
    # Formatage des colonnes
    ws.column_dimensions['A'].width = 40
    ws.column_dimensions['B'].width = 20
    
    # Masquer les lignes de grille
    ws.sheet_view.showGridLines = False
    
    return True

def fix_single_excel_file(file_path):
    """Corrige un fichier Excel individuel"""
    try:
        print(f"Traitement du fichier: {file_path}")
        wb = openpyxl.load_workbook(file_path)
        
        sheets_to_fix = []
        data_sheet = None
        
        # Identifier les feuilles à corriger
        for sheet_name in wb.sheetnames:
            if 'Synthèse' in sheet_name or 'Synth' in sheet_name:
                sheets_to_fix.append(sheet_name)
            elif sheet_name in ['Rapport', 'Data'] or data_sheet is None:
                data_sheet = wb[sheet_name]
        
        if not data_sheet:
            print(f"  [ATTENTION] Aucune feuille de données trouvée dans {file_path}")
            return False
        
        # Charger les données
        data = []
        headers = []
        
        # Lire les en-têtes (première ligne non vide)
        for row in data_sheet.iter_rows():
            if any(cell.value for cell in row):
                headers = [cell.value for cell in row]
                break
        
        if not headers:
            print(f"  [ERREUR] Impossible de lire les en-têtes dans {file_path}")
            return False
        
        # Lire les données
        for row in data_sheet.iter_rows(min_row=2, values_only=True):
            if any(cell for cell in row):  # Ignorer les lignes complètement vides
                data.append(row)
        
        if not data:
            print(f"  [ATTENTION] Aucune donnée trouvée dans {file_path}")
            return False
        
        df = pd.DataFrame(data, columns=headers)
        
        # Corriger ou créer les feuilles de synthèse
        for sheet_name in sheets_to_fix:
            print(f"  Correction de la feuille: {sheet_name}")
            ws = wb[sheet_name]
            
            # Déterminer le suffixe du titre
            if 'Globale' in sheet_name:
                title_suffix = " Globale"
            elif any(oc in str(file_path) for oc in ['BOUYGUES', 'FREE', 'ORANGE', 'IFT', 'SCOREFIT', 'SDFAST']):
                # Extraire l'opérateur du nom de fichier
                for oc in ['BOUYGUES', 'FREE', 'ORANGE', 'IFT', 'SCOREFIT', 'SDFAST']:
                    if oc in str(file_path):
                        title_suffix = f" - {oc}"
                        break
                else:
                    title_suffix = ""
            else:
                title_suffix = ""
            
            fix_synthesis_sheet(ws, df, title_suffix)
        
        # Si aucune feuille de synthèse n'existe, en créer une
        if not sheets_to_fix:
            print("  Création d'une nouvelle feuille de synthèse")
            ws_new = wb.create_sheet(title='Synthèse Facturation')
            fix_synthesis_sheet(ws_new, df)
        
        # Sauvegarder
        wb.save(file_path)
        print(f"  [OK] Fichier corrigé: {file_path}")
        return True
        
    except Exception as e:
        print(f"  [ERREUR] Échec correction de {file_path}: {str(e)}")
        return False

def fix_all_ftth_files(base_dir):
    """Corrige tous les fichiers FTTH dans le répertoire et ses sous-dossiers"""
    base_path = Path(base_dir)
    
    if not base_path.exists():
        print(f"[ERREUR] Répertoire introuvable: {base_dir}")
        return
    
    # Patterns de recherche pour les fichiers FTTH
    patterns = [
        "**/*FTTH*.xlsx",
        "**/FTTH_*.xlsx"
    ]
    
    files_found = []
    for pattern in patterns:
        files_found.extend(base_path.glob(pattern))
    
    # Supprimer les doublons et filtrer les fichiers temporaires
    unique_files = []
    seen = set()
    for file in files_found:
        if file.name not in seen and not file.name.startswith('~$'):
            unique_files.append(file)
            seen.add(file.name)
    
    if not unique_files:
        print(f"[ERREUR] Aucun fichier FTTH trouvé dans {base_dir}")
        return
    
    print(f"[INFO] {len(unique_files)} fichier(s) FTTH trouvé(s)")
    
    success_count = 0
    for file_path in unique_files:
        if fix_single_excel_file(file_path):
            success_count += 1
    
    print(f"\n[RESULTAT] {success_count}/{len(unique_files)} fichiers corrigés avec succès")

def main():
    """Fonction principale"""
    import sys
    
    if len(sys.argv) > 1:
        target_path = Path(sys.argv[1])
        if target_path.is_file():
            # Corriger un fichier spécifique
            fix_single_excel_file(target_path)
        elif target_path.is_dir():
            # Corriger tous les fichiers dans le répertoire
            fix_all_ftth_files(target_path)
        else:
            print(f"[ERREUR] Chemin invalide: {target_path}")
    else:
        # Par défaut, corriger tous les fichiers dans le répertoire KPI
        default_dir = r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_06"
        if Path(default_dir).exists():
            fix_all_ftth_files(default_dir)
        else:
            print(f"[ERREUR] Répertoire par défaut introuvable: {default_dir}")
            print("Usage: python comprehensive_excel_fix.py [fichier.xlsx|répertoire]")

if __name__ == "__main__":
    main()