import pandas as pd
import logging

def duplicate_signalization_tickets(df):
    """
    Duplique les lignes qui ont Ex_type non vide pour créer le couple SIGNALISATION + EXPERTISE.
    
    LOGIQUE EVOLUTIVE:
    - Un ticket SIGNALISATION se transforme en EXPERTISE
    - Quand Ex_type n'est pas vide, cela signifie qu'il y a eu évolution
    - On doit créer 2 lignes:
      1. SIGNALISATION (ligne originale transformée avec Ex_* -> colonnes standards)
      2. EXPERTISE (nouvelle ligne avec Date_New_exp, etc.)
    
    MAPPING des champs:
    SIGNALISATION:
    - Type = 'SIGNALISATION'  
    - Date ouverture du cas = Date ouverture originale
    - Date résolution = Date_resolution_SIG
    - Source_OC = Ex_source
    - Famille_OC = Ex_famille  
    - Diagnostic_OC = Ex_diagnostic
    - Mois résolution = Mois résol sig
    
    EXPERTISE:
    - Type = 'EXPERTISE'
    - Date ouverture du cas = Date_New_exp
    - Date résolution = Date résolution originale
    - Source_OC, Famille_OC, Diagnostic_OC = valeurs originales
    """
    
    if df.empty:
        logging.warning("DataFrame vide - aucune duplication possible")
        return df
    
    # Colonnes de signalisation à vérifier
    signalization_columns = [
        'Date_resolution_SIG',
        'Ex_type', 
        'Ex_source',
        'Ex_famille',
        'Ex_diagnostic',
        'Date_New_exp',
        'New_EXP',
        'Semaine résol sig',
        'Mois résol sig',
        'Semaine ouv new exp',
        'Mois ouv new exp',
        'Délai_JC2_Resol sig',
        'Délai_JC3_Resol exp',
        'Statut_SIG'
    ]
    
    # Vérifier quelles colonnes existent dans le DataFrame
    existing_sig_columns = [col for col in signalization_columns if col in df.columns]
    
    if not existing_sig_columns:
        logging.warning("Aucune colonne de signalisation trouvée dans le DataFrame")
        return df
    
    logging.info(f"Colonnes de signalisation trouvées: {existing_sig_columns}")
    
    # Identifier les lignes avec Ex_type non vide (évolution SIGNALISATION -> EXPERTISE)
    if 'Ex_type' not in df.columns:
        logging.warning("Colonne 'Ex_type' non trouvée - pas de dédoublement possible")
        return df
    
    # Filtrer les lignes qui ont Ex_type = 'SIGNALISATION' (indique une évolution)
    evolution_mask = (df['Ex_type'] == 'SIGNALISATION')
    evolution_rows = df[evolution_mask].copy()
    
    if evolution_rows.empty:
        logging.info("Aucune ligne avec Ex_type='SIGNALISATION' trouvée - pas d'évolution à traiter")
        return df
    
    logging.info(f"Nombre de lignes avec Ex_type='SIGNALISATION' trouvées: {len(evolution_rows)}")
    
    # CRÉATION DES LIGNES SIGNALISATION (transformation de la ligne originale)
    signalization_rows = evolution_rows.copy()
    signalization_rows['Type'] = 'SIGNALISATION'
    
    # CRÉATION DES LIGNES EXPERTISE (nouvelle ligne pour l'évolution) 
    expertise_rows = evolution_rows.copy()
    expertise_rows['Type'] = 'EXPERTISE'
    
    # MAPPING POUR SIGNALISATION (Ex_* -> colonnes standards)
    signalization_mapping = {
        'Ex_source': 'Souce_OC',      # Source OC pour SIGNALISATION  
        'Ex_famille': 'Famille_OC',   # Famille OC pour SIGNALISATION
        'Ex_diagnostic': 'Diagnostic_OC',  # Diagnostic OC pour SIGNALISATION
        'Date_resolution_SIG': 'Date résolution',  # Date résolution SIGNALISATION
        'Mois résol sig': 'Mois_Résolution',  # CORRIGÉ: Mois résolution SIGNALISATION
        'Délai_JC2_Resol sig': 'Délai résol JC'  # Délai SIGNALISATION
        # NOTE: Mois_Création reste inchangé (date ouverture originale)
    }
    
    # MAPPING POUR EXPERTISE (nouvelles données)  
    expertise_mapping = {
        'Date_New_exp': 'Date ouverture du cas',  # Nouvelle date d'ouverture EXPERTISE
        'Mois ouv new exp': 'Mois_Création',  # CORRIGÉ: Nouveau mois création EXPERTISE
        'Semaine ouv new exp': 'semaine ouv',  # Nouvelle semaine ouverture EXPERTISE
        'Délai_JC3_Resol exp': 'Délai résol JC'  # Délai résolution EXPERTISE
        # NOTE: Mois_Résolution reste inchangé (date résolution originale)
    }
    
    # Appliquer le mapping SIGNALISATION
    for ex_col, target_col in signalization_mapping.items():
        if ex_col in signalization_rows.columns and target_col in signalization_rows.columns:
            mask = signalization_rows[ex_col].notna() & (signalization_rows[ex_col] != '')
            signalization_rows.loc[mask, target_col] = signalization_rows.loc[mask, ex_col]
    
    # Appliquer le mapping EXPERTISE  
    for ex_col, target_col in expertise_mapping.items():
        if ex_col in expertise_rows.columns and target_col in expertise_rows.columns:
            mask = expertise_rows[ex_col].notna() & (expertise_rows[ex_col] != '')
            expertise_rows.loc[mask, target_col] = expertise_rows.loc[mask, ex_col]
    
    # NORMALISATION CRITIQUE: Uniformiser le format des champs Mois_* 
    # Supprimer TOUS les espaces parasites (normaux, insécables, etc.) pour avoir un format cohérent
    for df_part, name in [(signalization_rows, "SIGNALISATION"), (expertise_rows, "EXPERTISE")]:
        for mois_col in ['Mois_Création', 'Mois_Résolution']:
            if mois_col in df_part.columns:
                # Méthode robuste: supprimer tous types d'espaces (normal, insécable, etc.)
                original_vals = df_part[mois_col].astype(str).tolist()
                
                # Appliquer plusieurs nettoyages pour couvrir tous types d'espaces
                df_part[mois_col] = (df_part[mois_col]
                    .astype(str)
                    .str.replace(' ', '', regex=False)     # Espace normal (U+0020)
                    .str.replace('\u00A0', '', regex=False) # Espace insécable (U+00A0) 
                    .str.replace('\u2009', '', regex=False) # Espace fine (U+2009)
                    .str.replace('\u202F', '', regex=False) # Espace insécable étroite (U+202F)
                    .str.replace(r'\s+', '', regex=True))   # Tous autres espaces
                
                cleaned_vals = df_part[mois_col].astype(str).tolist()
                
                # Log des changements
                changes = 0
                for orig, clean in zip(original_vals, cleaned_vals):
                    if orig != clean:
                        changes += 1
                        logging.info(f"  {name} {mois_col}: \"{orig}\" -> \"{clean}\"")
                
                if changes > 0:
                    logging.info(f"Format {mois_col} normalisé pour {name}: {changes} valeur(s) modifiée(s)")
                else:
                    logging.info(f"Format {mois_col} déjà propre pour {name}")
    
    # Log détaillé des duplications
    for idx, row in evolution_rows.iterrows():
        ref_oc = row.get('Ref OC', 'N/A')
        identifiant = row.get('Identifiant cas', 'N/A')
        ex_type = row.get('Ex_type', 'N/A')
        logging.info(f"Évolution ticket {identifiant} ({ref_oc}): création SIGNALISATION + EXPERTISE")
        logging.info(f"  Ex_type: {ex_type}")
        
        # Log des données de signalisation
        sig_data = []
        for col in ['Ex_source', 'Ex_famille', 'Ex_diagnostic', 'Date_resolution_SIG']:
            if col in row.index and pd.notna(row[col]) and row[col] != '':
                sig_data.append(f"{col}={row[col]}")
        
        if sig_data:
            logging.info(f"  Données SIGNALISATION: {', '.join(sig_data[:3])}{'...' if len(sig_data) > 3 else ''}")
            
        # Log des données d'expertise
        exp_data = []
        for col in ['Date_New_exp', 'Mois ouv new exp']:
            if col in row.index and pd.notna(row[col]) and row[col] != '':
                exp_data.append(f"{col}={row[col]}")
        
        if exp_data:
            logging.info(f"  Données EXPERTISE: {', '.join(exp_data)}")
    
    # Supprimer les lignes originales qui ont évolué et les remplacer par les nouvelles
    df_without_evolved = df[~evolution_mask].copy()
    
    # IMPORTANT: Assurer l'ordre chronologique correct (SIGNALISATION avant EXPERTISE)
    # Réinitialiser les index pour contrôler l'ordre
    signalization_rows = signalization_rows.reset_index(drop=True)
    expertise_rows = expertise_rows.reset_index(drop=True)
    
    # Ajuster les index pour que SIGNALISATION soit toujours avant EXPERTISE
    max_idx = max(df_without_evolved.index) if not df_without_evolved.empty else -1
    signalization_rows.index = range(max_idx + 1, max_idx + 1 + len(signalization_rows))
    expertise_rows.index = range(max_idx + 1 + len(signalization_rows), max_idx + 1 + len(signalization_rows) + len(expertise_rows))
    
    # Concaténer avec les nouvelles lignes SIGNALISATION et EXPERTISE (ordre préservé)
    result_df = pd.concat([df_without_evolved, signalization_rows, expertise_rows], ignore_index=False)
    
    # Trier par Identifiant cas en préservant l'ordre des lignes (tri stable)
    if 'Identifiant cas' in result_df.columns:
        # Tri stable préserve l'ordre relatif (SIGNALISATION puis EXPERTISE)
        result_df = result_df.sort_values('Identifiant cas', kind='stable').reset_index(drop=True)
    
    # Calculs des statistiques
    added_lines = len(signalization_rows) + len(expertise_rows)
    original_lines = len(evolution_rows)
    
    logging.info(f"Évolution terminée: {len(df)} -> {len(result_df)} lignes")
    logging.info(f"Lignes avec évolution traitées: {original_lines}")
    logging.info(f"Nouvelles lignes créées: {added_lines} ({len(signalization_rows)} SIGNALISATION + {len(expertise_rows)} EXPERTISE)")
    
    # Statistiques finales
    if 'Type' in result_df.columns:
        type_counts = result_df['Type'].value_counts()
        logging.info("Répartition finale des types:")
        for type_name, count in type_counts.items():
            logging.info(f"  {type_name}: {count} lignes")
    
    return result_df