#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Correctif rapide en lot pour les fichiers FTTH
"""

from pathlib import Path
import subprocess
import sys

def run_fix_on_file(script_path, file_path):
    """Exécute le correctif sur un fichier spécifique"""
    try:
        result = subprocess.run(
            [sys.executable, str(script_path), str(file_path)],
            capture_output=True,
            text=True,
            timeout=30  # 30 secondes max par fichier
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Timeout - fichier trop volumineux"
    except Exception as e:
        return False, "", str(e)

def main():
    # Chemins
    base_dir = Path(r"C:\Users\<USER>\Desktop\KPI\Facturation_STT\2025_08_06")
    script_path = Path("src/comprehensive_excel_fix.py")
    
    if not base_dir.exists():
        print(f"[ERREUR] Répertoire introuvable: {base_dir}")
        return
    
    if not script_path.exists():
        print(f"[ERREUR] Script introuvable: {script_path}")
        return
    
    # Trouver tous les fichiers FTTH
    files_to_fix = [
        base_dir / "FTTH_Reporting_Final_2025_06.xlsx"  # Fichier principal
    ]
    
    # Ajouter les fichiers dans les sous-dossiers
    for subdir in ["2025_06/SFRA", "2025_06/XPF"]:
        subdir_path = base_dir / subdir
        if subdir_path.exists():
            files_to_fix.extend(subdir_path.glob("FTTH_*.xlsx"))
    
    # Filtrer les fichiers qui existent
    existing_files = [f for f in files_to_fix if f.exists() and not f.name.startswith('~$')]
    
    print(f"[INFO] {len(existing_files)} fichier(s) à traiter")
    
    success_count = 0
    for file_path in existing_files:
        print(f"\nTraitement: {file_path.name}")
        success, stdout, stderr = run_fix_on_file(script_path, file_path)
        
        if success:
            print(f"  [OK] Corrigé avec succès")
            success_count += 1
        else:
            print(f"  [ERREUR] Échec: {stderr}")
    
    print(f"\n[RESULTAT] {success_count}/{len(existing_files)} fichiers corrigés")

if __name__ == "__main__":
    main()