#!/usr/bin/env python3
"""
Debug du problème de dédoublement avec l'exemple réel fourni
"""

import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from signalization_duplication_logic import duplicate_signalization_tickets

def test_with_real_example():
    """Test avec l'exemple réel fourni par l'utilisateur"""
    
    print("=== TEST AVEC EXEMPLE REEL ===")
    
    # Données de l'exemple fourni
    test_data = {
        'statut': ['Clos'],
        'Identifiant cas': ['C34048200'],
        'Ref OC': ['ISA00120250519008803'],
        'Date ouverture du cas': ['19/05/2025'],
        'Date de clôture du cas': ['05/06/2025'],
        'OC': ['IFT'],
        'Prise': ['SF-000A-2L61'],
        'Code retour': ['Transmis à tort'],
        'TT_OC_JO_Date de résolution': ['05/06/2025'],
        'Type': ['EXPERTISE'],  # Type actuel
        'Diagnostic_OC': ['STT01#PAS DE DEFAUT CONSTATE S'],
        'Souce_OC': ['OC'],  # Typo volontaire comme dans le vrai fichier
        'Famille_OC': ['PM-PBO'],
        'semaine ouv': ['2025_21'],
        'Mois Ouv': ['2025_05'],
        'Mois résolution': ['2025_06'],
        'semaine résol': ['2025_23'],
        'Délai résol JC': [17.305219],
        'En cours / Clos': [''],
        'En cours +7J': [''],
        'En cours +14': [''],
        'En cours +21J': [''],
        'A pour cas parent': [''],
        'Statut du cas': [''],
        'Region': [''],
        'DEPT': [''],
        'Elément impacté': [''],
        'NRO': [''],
        'Zone': [''],
        'STIT': [''],
        'Date création du cas': [''],
        '000_FTTH_tickets_STIT_ssDBL_FULL_Date de résolution': [''],
        'Source du problème': [''],
        'Famille diagnostic': [''],
        'Diagnostic': [''],
        'Propriétaire - LTDET': [''],
        'délOuvEnfantParent': [''],
        'délFermEnfantParent': [''],
        'Délai résol JC cas parent': [''],
        'Plus de + 90JOURS': [''],
        
        # CHAMPS DE SIGNALISATION - Ces champs sont VIDES dans cet exemple !
        'Date_resolution_SIG': [''],
        'Ex_type': [''],  # VIDE ! C'est ça le problème
        'Ex_source': [''],
        'Ex_famille': [''],
        'Ex_diagnostic': [''],
        'Date_New_exp': [''],
        'New_EXP': [''],
        'Semaine résol sig': [''],
        'Mois résol sig': [''],
        'Semaine ouv new exp': [''],
        'Mois ouv new exp': [''],
        'Délai_JC2_Resol sig': [''],
        'Délai_JC3_Resol exp': [''],
        'Statut_SIG': [''],
        'OI': [''],
        'Code OI InterOP': ['']
    }
    
    df = pd.DataFrame(test_data)
    
    print(f"AVANT dédoublement:")
    print(f"  Lignes: {len(df)}")
    print(f"  Type: {df['Type'].iloc[0]}")
    print(f"  Ex_type: '{df['Ex_type'].iloc[0]}'")
    print(f"  Ex_type est vide? {df['Ex_type'].iloc[0] == ''}")
    print(f"  Ex_type notna()? {df['Ex_type'].notna().iloc[0]}")
    
    # Vérifier la condition de détection
    evolution_mask = df['Ex_type'].notna() & (df['Ex_type'] != '') & (df['Ex_type'] != 0)
    print(f"  Masque evolution: {evolution_mask.iloc[0]}")
    print(f"  Nombre de lignes détectées pour évolution: {evolution_mask.sum()}")
    
    if evolution_mask.sum() == 0:
        print("❌ PROBLÈME IDENTIFIÉ: Ex_type est vide, aucune évolution détectée!")
        print("   La logique actuelle cherche Ex_type NON VIDE pour détecter les évolutions")
        print("   Mais dans votre exemple, Ex_type est vide...")
        
        # Créer un exemple avec Ex_type rempli
        print("\n=== TEST AVEC Ex_type REMPLI ===")
        df_with_ex_type = df.copy()
        df_with_ex_type['Ex_type'] = ['SIGNALISATION']
        df_with_ex_type['Ex_source'] = ['SRC_SIG']
        df_with_ex_type['Ex_famille'] = ['FAM_SIG']
        df_with_ex_type['Ex_diagnostic'] = ['DIAG_SIG']
        df_with_ex_type['Date_resolution_SIG'] = ['01/06/2025']
        df_with_ex_type['Date_New_exp'] = ['03/06/2025']
        
        print(f"Ex_type après modification: '{df_with_ex_type['Ex_type'].iloc[0]}'")
        evolution_mask_fixed = df_with_ex_type['Ex_type'].notna() & (df_with_ex_type['Ex_type'] != '') & (df_with_ex_type['Ex_type'] != 0)
        print(f"Masque evolution corrigé: {evolution_mask_fixed.iloc[0]}")
        
        # Tester le dédoublement
        result = duplicate_signalization_tickets(df_with_ex_type)
        print(f"\nAPRÈS dédoublement (avec Ex_type rempli):")
        print(f"  Lignes: {len(result)} (était {len(df_with_ex_type)})")
        
        if len(result) > len(df_with_ex_type):
            print("✅ Dédoublement FONCTIONNE quand Ex_type est rempli!")
            for idx, row in result.iterrows():
                print(f"    Ligne {idx}: Type={row['Type']}, Ex_type={row['Ex_type']}")
        else:
            print("❌ Problème dans la logique de dédoublement")
    else:
        print("Ex_type non vide détecté, test du dédoublement...")
        result = duplicate_signalization_tickets(df)
        print(f"\nAPRÈS dédoublement:")
        print(f"  Lignes: {len(result)} (était {len(df)})")

def analyze_signalization_logic():
    """Analyse de la logique actuelle vs besoins réels"""
    
    print("\n=== ANALYSE DE LA LOGIQUE ===")
    print("LOGIQUE ACTUELLE:")
    print("  - Cherche les lignes avec Ex_type NON VIDE")
    print("  - Crée SIGNALISATION + EXPERTISE pour ces lignes")
    print("")
    print("PROBLÈME IDENTIFIÉ:")
    print("  - Dans votre exemple, Ex_type est VIDE")
    print("  - Donc aucune évolution détectée")
    print("  - Donc aucun dédoublement")
    print("")
    print("QUESTION:")
    print("  - Les champs Ex_* sont-ils vraiment vides dans vos données?")
    print("  - Ou faut-il changer la logique de détection?")
    print("")
    print("SOLUTIONS POSSIBLES:")
    print("  1. Vérifier que Ex_type est bien rempli dans le fichier source")
    print("  2. Modifier la logique pour détecter autrement les évolutions") 
    print("  3. Analyser un échantillon du vrai fichier pour comprendre la structure")

if __name__ == "__main__":
    test_with_real_example()
    analyze_signalization_logic()